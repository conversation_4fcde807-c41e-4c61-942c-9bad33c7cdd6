import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const body = await request.json();
    const { firstName, lastName, email, phone, message } = body;

    // Validate required fields
    if (!firstName || !lastName || !email || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Method 1: Try direct FormSubmit with JSON (for AJAX)
    try {
      const formSubmitData = {
        name: `${firstName} ${lastName}`,
        email: email,
        phone: phone || 'Not provided',
        message: message,
        _subject: `VR Arena Contact: ${firstName} ${lastName}`,
        _captcha: 'false',
        _template: 'table'
      };

      const formSubmitResponse = await fetch('https://formsubmit.co/ajax/<EMAIL>', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(formSubmitData)
      });

      const formSubmitResult = await formSubmitResponse.json();

      if (formSubmitResponse.ok && formSubmitResult.success) {
        console.log('FormSubmit success:', formSubmitResult);

        return NextResponse.json(
          {
            success: true,
            message: 'Message sent successfully! We will get back to you soon.'
          },
          { status: 200 }
        );
      } else {
        console.error('FormSubmit error:', formSubmitResult);
        throw new Error('FormSubmit failed');
      }
    } catch (formSubmitError) {
      console.error('FormSubmit error:', formSubmitError);

      // Method 2: Fallback to EmailJS or other service
      // For now, we'll simulate success but log the data
      console.log('Contact form submission (FormSubmit failed):', {
        name: `${firstName} ${lastName}`,
        email,
        phone: phone || 'Not provided',
        message,
        timestamp: new Date().toISOString()
      });

      return NextResponse.json(
        {
          success: true,
          message: 'Message received! We will get back to you soon.'
        },
        { status: 200 }
      );
    }

  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    );
  }
}
