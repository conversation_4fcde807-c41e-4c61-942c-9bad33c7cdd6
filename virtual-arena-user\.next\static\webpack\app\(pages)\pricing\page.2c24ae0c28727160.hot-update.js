"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/pricing/page",{

/***/ "(app-pages-browser)/./src/app/(pages)/pricing/Plans.jsx":
/*!*******************************************!*\
  !*** ./src/app/(pages)/pricing/Plans.jsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_translations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/translations */ \"(app-pages-browser)/./src/app/translations.js\");\n/* harmony import */ var _PricingCalculator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PricingCalculator */ \"(app-pages-browser)/./src/app/(pages)/pricing/PricingCalculator.jsx\");\n/* harmony import */ var _app_utils_currency__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/utils/currency */ \"(app-pages-browser)/./src/app/utils/currency.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Plans = (param)=>{\n    let { locale = \"en\" } = param;\n    const t = _app_translations__WEBPACK_IMPORTED_MODULE_2__.translations[locale] || _app_translations__WEBPACK_IMPORTED_MODULE_2__.translations.en;\n    const basic = [\n        t.basicFeature1,\n        t.basicFeature2,\n        t.basicFeature3,\n        t.basicFeature4\n    ];\n    const premium = [\n        t.premiumFeature1,\n        t.premiumFeature2,\n        t.premiumFeature3,\n        t.premiumFeature4,\n        t.premiumFeature5\n    ];\n    const ultimate = [\n        t.ultimateFeature1,\n        t.ultimateFeature2,\n        t.ultimateFeature3,\n        t.ultimateFeature4,\n        t.ultimateFeature5,\n        t.ultimateFeature6\n    ];\n    const family = [\n        t.familyFeature1,\n        t.familyFeature2,\n        t.familyFeature3,\n        t.familyFeature4\n    ];\n    const group = [\n        t.groupFeature1,\n        t.groupFeature2,\n        t.groupFeature3,\n        t.groupFeature4\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"events\",\n        className: \"w-full h-full bg-blackish\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full mx-auto max-w-[1600px] border-y pt-[100px] pb-[51px] flex-col flex items-center px-4 md:px-10 lg:px-16 xl:px-20 2xl:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-[#DB1FEB] text-3xl font-bold mb-2\",\n                            children: t.allInclusivePricing\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 53,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white text-xl\",\n                            children: t.whatYouSeeIsWhatYouPay\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 54,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 52,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PricingCalculator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    locale: locale\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 58,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] rounded-xl w-[303px] px-5 py-[14px] flex gap-2 mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"bg-white text-[26px] font-semibold px-8 py-4 rounded-xl text-gradnt\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gradiant\",\n                                children: \"Monthly pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 105\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                        lineNumber: 61,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 60,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-y-8 mt-[60px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.basicPlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.basicPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.basicDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: basic.map((plan, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    plan\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 66,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 65,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.premiumPlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.premiumPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.premiumDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: premium.map((plan, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    plan\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 87,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.ultimatePlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.ultimatePrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.ultimateDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: ultimate.map((plan, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    plan\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 110,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 109,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 64,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.familyPlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.familyPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.familyDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: family.map((feature, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    feature\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 136,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 135,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.groupPlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.groupPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.groupDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: group.map((feature, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    feature\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 158,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 157,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 134,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-white text-3xl font-bold text-center mb-10\",\n                            children: t.hourlyAndDailyPasses\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 183,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.oneHourPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.oneHourPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.unlimitedAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.twoHourPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.twoHourPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.unlimitedAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.halfDayPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.halfDayPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.fourHoursAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.fullDayPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.fullDayPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.allDayAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.weekendPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.weekendPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.weekendAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 184,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 182,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-white text-3xl font-bold text-center mb-10\",\n                            children: t.individualExperiencePricing\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 215,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.freeRoamingArena\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(12, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(20, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.ufoSpaceshipCinema\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(9, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(15, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.vr360\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(9, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(15, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.vrBattle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(9, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(15, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.vrWarrior\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(7, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(12, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.vrCat\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(6, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(10, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 216,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 214,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-white text-3xl font-bold text-center mb-10\",\n                            children: t.groupDiscounts\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 293,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 rounded-xl p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white text-xl font-bold mb-3\",\n                                                children: \"5-9 People\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[#23A1FF] text-3xl font-bold\",\n                                                children: \"10% OFF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white text-xl font-bold mb-3\",\n                                                children: \"10-19 People\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[#23A1FF] text-3xl font-bold\",\n                                                children: \"15% OFF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white text-xl font-bold mb-3\",\n                                                children: \"20+ People\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[#23A1FF] text-3xl font-bold\",\n                                                children: \"20% OFF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 295,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 294,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 292,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-white text-3xl font-bold text-center mb-10\",\n                            children: t.specialEventPackages\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 314,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.birthdayPackages\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white mb-4\",\n                                            children: t.birthdayPackagesDesc\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.startingPrice\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(249, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-2\",\n                                            children: t.birthdayPackagesIncludes\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.corporateTeamBuilding\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white mb-4\",\n                                            children: t.corporateTeamBuildingDesc\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.perPerson\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(45, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-2\",\n                                            children: t.corporateTeamBuildingCustomizable\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.schoolFieldTrips\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white mb-4\",\n                                            children: t.schoolFieldTripsDesc\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.perStudent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(25, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-2\",\n                                            children: t.teacherChaperonePasses\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 315,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 313,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n            lineNumber: 50,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n        lineNumber: 49,\n        columnNumber: 9\n    }, undefined);\n};\n_c = Plans;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Plans);\nvar _c;\n$RefreshReg$(_c, \"Plans\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(pages)/pricing/Plans.jsx\n"));

/***/ })

});