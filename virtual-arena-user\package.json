{"name": "virtual-arena", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@eslint/compat": "^1.3.0", "@headlessui/react": "^2.2.4", "@paypal/react-paypal-js": "^8.2.0", "@reduxjs/toolkit": "^2.5.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^6.0.0", "axios": "^1.7.9", "framer-motion": "^12.4.7", "fs": "^0.0.1-security", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "next": "^14.2.3", "next-intl": "^4.1.0", "path": "^0.12.7", "pusher-js": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.1", "react-icons": "^5.4.0", "react-modal": "^3.16.3", "react-redux": "^9.2.0", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "stripe": "^18.2.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8.5.4", "tailwindcss": "^3.4.1"}}