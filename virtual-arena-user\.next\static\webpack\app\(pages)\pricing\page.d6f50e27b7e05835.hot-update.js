"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/pricing/page",{

/***/ "(app-pages-browser)/./src/app/(pages)/pricing/Plans.jsx":
/*!*******************************************!*\
  !*** ./src/app/(pages)/pricing/Plans.jsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_translations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/translations */ \"(app-pages-browser)/./src/app/translations.js\");\n/* harmony import */ var _PricingCalculator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PricingCalculator */ \"(app-pages-browser)/./src/app/(pages)/pricing/PricingCalculator.jsx\");\n/* harmony import */ var _app_utils_currency__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/utils/currency */ \"(app-pages-browser)/./src/app/utils/currency.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Plans = (param)=>{\n    let { locale = \"en\" } = param;\n    const t = _app_translations__WEBPACK_IMPORTED_MODULE_2__.translations[locale] || _app_translations__WEBPACK_IMPORTED_MODULE_2__.translations.en;\n    const basic = [\n        t.basicFeature1,\n        t.basicFeature2,\n        t.basicFeature3,\n        t.basicFeature4\n    ];\n    const premium = [\n        t.premiumFeature1,\n        t.premiumFeature2,\n        t.premiumFeature3,\n        t.premiumFeature4,\n        t.premiumFeature5\n    ];\n    const ultimate = [\n        t.ultimateFeature1,\n        t.ultimateFeature2,\n        t.ultimateFeature3,\n        t.ultimateFeature4,\n        t.ultimateFeature5,\n        t.ultimateFeature6\n    ];\n    const family = [\n        t.familyFeature1,\n        t.familyFeature2,\n        t.familyFeature3,\n        t.familyFeature4\n    ];\n    const group = [\n        t.groupFeature1,\n        t.groupFeature2,\n        t.groupFeature3,\n        t.groupFeature4\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"events\",\n        className: \"w-full h-full bg-blackish\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full mx-auto max-w-[1600px] border-y pt-[100px] pb-[51px] flex-col flex items-center px-4 md:px-10 lg:px-16 xl:px-20 2xl:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-[#DB1FEB] text-3xl font-bold mb-2\",\n                            children: t.allInclusivePricing\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 53,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white text-xl\",\n                            children: t.whatYouSeeIsWhatYouPay\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 54,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 52,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PricingCalculator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    locale: locale\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 58,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] rounded-xl w-[303px] px-5 py-[14px] flex gap-2 mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"bg-white text-[26px] font-semibold px-8 py-4 rounded-xl text-gradnt\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gradiant\",\n                                children: t.monthly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 105\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                        lineNumber: 61,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 60,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-y-8 mt-[60px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.basicPlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.basicPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.basicDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: basic.map((plan, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    plan\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 66,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 65,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.premiumPlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.premiumPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.premiumDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: premium.map((plan, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    plan\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 87,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.ultimatePlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.ultimatePrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.ultimateDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: ultimate.map((plan, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    plan\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 110,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 109,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 64,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.familyPlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.familyPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.familyDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: family.map((feature, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    feature\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 136,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 135,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-[20px] p-6 md:py-[31px] md:px-[33px] border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-[20px] border border-[#23A1FF] flex flex-col justify-center items-center py-[30px] drop-shadow-xl shadow-[#209FFF]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/assets/shdow.png\",\n                                                alt: \"\",\n                                                className: \"absolute top-0 h-full z-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-white text-[26px] font-semibold\",\n                                                children: t.groupPlan\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text- text-[26px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#23A1FF] text-[40px] md:text-[50px] font-semibold\",\n                                                        children: t.groupPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-[18px] \",\n                                                        children: t.perMonth\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-white mt-[28px] pb-[25px] leading-none border-b\",\n                                        children: t.groupDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2.5 mt-[34px]\",\n                                        children: group.map((feature, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1.5 text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/icons/check.png\",\n                                                        alt: \"\",\n                                                        className: \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    feature\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 37\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xl mt-[43px] font-semibold flex items-center py-2 md:py-4 px-6 md:px-8 text-white rounded-full bg-gradient-to-tr from-[#926BB9] via-[#5A79FB] to-[#2FBCF7] \",\n                                        children: [\n                                            t.bookNow,\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/icons/arrow.svg\",\n                                                alt: \"\",\n                                                className: \"h-[22px] w-[22px] ml-[11px] rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 222\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 158,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 157,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 134,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-white text-3xl font-bold text-center mb-10\",\n                            children: t.hourlyAndDailyPasses\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 183,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.oneHourPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.oneHourPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.unlimitedAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.twoHourPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.twoHourPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.unlimitedAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.halfDayPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.halfDayPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.fourHoursAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.fullDayPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.fullDayPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.allDayAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-2\",\n                                            children: t.weekendPass\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#23A1FF] text-3xl font-bold mb-1\",\n                                            children: t.weekendPrice\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm\",\n                                            children: t.weekendAccess\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 184,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 182,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-white text-3xl font-bold text-center mb-10\",\n                            children: t.individualExperiencePricing\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 215,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.freeRoamingArena\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(12, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(20, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.ufoSpaceshipCinema\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(9, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(15, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.vr360\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(9, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(15, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.vrBattle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(9, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(15, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.vrWarrior\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(7, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(12, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.vrCat\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.singleSession\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(6, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.twoSessions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(10, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 216,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 214,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-white text-3xl font-bold text-center mb-10\",\n                            children: t.groupDiscounts\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 293,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 rounded-xl p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white text-xl font-bold mb-3\",\n                                                children: \"5-9 People\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[#23A1FF] text-3xl font-bold\",\n                                                children: \"10% OFF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white text-xl font-bold mb-3\",\n                                                children: \"10-19 People\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[#23A1FF] text-3xl font-bold\",\n                                                children: \"15% OFF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white text-xl font-bold mb-3\",\n                                                children: \"20+ People\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[#23A1FF] text-3xl font-bold\",\n                                                children: \"20% OFF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                lineNumber: 295,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 294,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 292,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mt-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-white text-3xl font-bold text-center mb-10\",\n                            children: t.specialEventPackages\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 314,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.birthdayPackages\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white mb-4\",\n                                            children: t.birthdayPackagesDesc\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.startingPrice\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(249, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-2\",\n                                            children: t.birthdayPackagesIncludes\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.corporateTeamBuilding\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white mb-4\",\n                                            children: t.corporateTeamBuildingDesc\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.perPerson\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(45, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-2\",\n                                            children: t.corporateTeamBuildingCustomizable\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white text-xl font-bold mb-4\",\n                                            children: t.schoolFieldTrips\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white mb-4\",\n                                            children: t.schoolFieldTripsDesc\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-b border-gray-700 pb-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: t.perStudent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-white\",\n                                                    children: (0,_app_utils_currency__WEBPACK_IMPORTED_MODULE_4__.formatDisplayPrice)(25, locale)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-2\",\n                                            children: t.teacherChaperonePasses\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                            lineNumber: 315,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n                    lineNumber: 313,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n            lineNumber: 50,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\(pages)\\\\pricing\\\\Plans.jsx\",\n        lineNumber: 49,\n        columnNumber: 9\n    }, undefined);\n};\n_c = Plans;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Plans);\nvar _c;\n$RefreshReg$(_c, \"Plans\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKHBhZ2VzKS9wcmljaW5nL1BsYW5zLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUN5QjtBQUN3QjtBQUNFO0FBQ007QUFFekQsTUFBTUksUUFBUTtRQUFDLEVBQUVDLFNBQVMsSUFBSSxFQUFFO0lBQzVCLE1BQU1DLElBQUlMLDJEQUFZLENBQUNJLE9BQU8sSUFBSUosMkRBQVlBLENBQUNNLEVBQUU7SUFFakQsTUFBTUMsUUFBUTtRQUNWRixFQUFFRyxhQUFhO1FBQ2ZILEVBQUVJLGFBQWE7UUFDZkosRUFBRUssYUFBYTtRQUNmTCxFQUFFTSxhQUFhO0tBQ2xCO0lBRUQsTUFBTUMsVUFBVTtRQUNaUCxFQUFFUSxlQUFlO1FBQ2pCUixFQUFFUyxlQUFlO1FBQ2pCVCxFQUFFVSxlQUFlO1FBQ2pCVixFQUFFVyxlQUFlO1FBQ2pCWCxFQUFFWSxlQUFlO0tBQ3BCO0lBRUQsTUFBTUMsV0FBVztRQUNiYixFQUFFYyxnQkFBZ0I7UUFDbEJkLEVBQUVlLGdCQUFnQjtRQUNsQmYsRUFBRWdCLGdCQUFnQjtRQUNsQmhCLEVBQUVpQixnQkFBZ0I7UUFDbEJqQixFQUFFa0IsZ0JBQWdCO1FBQ2xCbEIsRUFBRW1CLGdCQUFnQjtLQUNyQjtJQUVELE1BQU1DLFNBQVM7UUFDWHBCLEVBQUVxQixjQUFjO1FBQ2hCckIsRUFBRXNCLGNBQWM7UUFDaEJ0QixFQUFFdUIsY0FBYztRQUNoQnZCLEVBQUV3QixjQUFjO0tBQ25CO0lBRUQsTUFBTUMsUUFBUTtRQUNWekIsRUFBRTBCLGFBQWE7UUFDZjFCLEVBQUUyQixhQUFhO1FBQ2YzQixFQUFFNEIsYUFBYTtRQUNmNUIsRUFBRTZCLGFBQWE7S0FDbEI7SUFFRCxxQkFDSSw4REFBQ0M7UUFBSUMsSUFBRztRQUFTQyxXQUFZO2tCQUN6Qiw0RUFBQ0Y7WUFBSUUsV0FBVTs7OEJBRVgsOERBQUNGO29CQUFJRSxXQUFVOztzQ0FDWCw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQTBDaEMsRUFBRWtDLG1CQUFtQjs7Ozs7O3NDQUM3RSw4REFBQ0M7NEJBQUVILFdBQVU7c0NBQXNCaEMsRUFBRW9DLHNCQUFzQjs7Ozs7Ozs7Ozs7OzhCQUkvRCw4REFBQ3hDLDBEQUFpQkE7b0JBQUNHLFFBQVFBOzs7Ozs7OEJBRTNCLDhEQUFDK0I7b0JBQUlFLFdBQVU7OEJBQ1gsNEVBQUNDO3dCQUFHRCxXQUFVOzswQ0FBc0UsOERBQUNLO2dDQUFLTCxXQUFVOzBDQUFpQmhDLEVBQUVzQyxPQUFPOzs7Ozs7NEJBQVE7Ozs7Ozs7Ozs7Ozs4QkFHMUksOERBQUNSO29CQUFJRSxXQUFVOztzQ0FDWCw4REFBQ0Y7NEJBQUlFLFdBQVU7c0NBQ1gsNEVBQUNGO2dDQUFJRSxXQUFVOztrREFDWCw4REFBQ0Y7d0NBQUlFLFdBQVU7OzBEQUNYLDhEQUFDTztnREFBSUMsS0FBSTtnREFBb0JDLEtBQUk7Z0RBQUdULFdBQVU7Ozs7OzswREFDOUMsOERBQUNVO2dEQUFHVixXQUFVOzBEQUF3Q2hDLEVBQUUyQyxTQUFTOzs7Ozs7MERBQ2pFLDhEQUFDRDtnREFBR1YsV0FBVTs7a0VBQ1YsOERBQUNLO3dEQUFLTCxXQUFVO2tFQUEyRGhDLEVBQUU0QyxVQUFVOzs7Ozs7a0VBQ3ZGLDhEQUFDUDt3REFBS0wsV0FBVTtrRUFBMkJoQyxFQUFFNkMsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUc3RCw4REFBQ1Y7d0NBQUVILFdBQVU7a0RBQWdFaEMsRUFBRThDLFNBQVM7Ozs7OztrREFDeEYsOERBQUNoQjt3Q0FBSUUsV0FBVTtrREFDVjlCLE1BQU02QyxHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsa0JBQ2QsOERBQUNuQjtnREFBWUUsV0FBVTs7a0VBQ25CLDhEQUFDTzt3REFBSUMsS0FBSTt3REFBbUJDLEtBQUk7d0RBQUdULFdBQVU7Ozs7OztvREFDNUNnQjs7K0NBRktDOzs7Ozs7Ozs7O2tEQU1sQiw4REFBQ0M7d0NBQU9sQixXQUFVOzs0Q0FBb0toQyxFQUFFbUQsT0FBTzs0Q0FBQzswREFBQyw4REFBQ1o7Z0RBQUlDLEtBQUk7Z0RBQW1CQyxLQUFJO2dEQUFHVCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHdFAsOERBQUNGOzRCQUFJRSxXQUFVO3NDQUNYLDRFQUFDRjtnQ0FBSUUsV0FBVTs7a0RBQ1gsOERBQUNGO3dDQUFJRSxXQUFVOzswREFDWCw4REFBQ087Z0RBQUlDLEtBQUk7Z0RBQW9CQyxLQUFJO2dEQUFHVCxXQUFVOzs7Ozs7MERBQzlDLDhEQUFDVTtnREFBR1YsV0FBVTswREFBd0NoQyxFQUFFb0QsV0FBVzs7Ozs7OzBEQUNuRSw4REFBQ1Y7Z0RBQUdWLFdBQVU7O2tFQUNWLDhEQUFDSzt3REFBS0wsV0FBVTtrRUFBMkRoQyxFQUFFcUQsWUFBWTs7Ozs7O2tFQUN6Riw4REFBQ2hCO3dEQUFLTCxXQUFVO2tFQUEyQmhDLEVBQUU2QyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzdELDhEQUFDVjt3Q0FBRUgsV0FBVTtrREFBZ0VoQyxFQUFFc0QsV0FBVzs7Ozs7O2tEQUMxRiw4REFBQ3hCO3dDQUFJRSxXQUFVO2tEQUNWekIsUUFBUXdDLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxrQkFDaEIsOERBQUNuQjtnREFBWUUsV0FBVTs7a0VBQ25CLDhEQUFDTzt3REFBSUMsS0FBSTt3REFBbUJDLEtBQUk7d0RBQUdULFdBQVU7Ozs7OztvREFDNUNnQjs7K0NBRktDOzs7Ozs7Ozs7O2tEQU1sQiw4REFBQ0M7d0NBQU9sQixXQUFVOzs0Q0FBb0toQyxFQUFFbUQsT0FBTzs0Q0FBQzswREFBQyw4REFBQ1o7Z0RBQUlDLEtBQUk7Z0RBQW1CQyxLQUFJO2dEQUFHVCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHdFAsOERBQUNGOzRCQUFJRSxXQUFVO3NDQUNYLDRFQUFDRjtnQ0FBSUUsV0FBVTs7a0RBQ1gsOERBQUNGO3dDQUFJRSxXQUFVOzswREFDWCw4REFBQ087Z0RBQUlDLEtBQUk7Z0RBQW9CQyxLQUFJO2dEQUFHVCxXQUFVOzs7Ozs7MERBQzlDLDhEQUFDVTtnREFBR1YsV0FBVTswREFBd0NoQyxFQUFFdUQsWUFBWTs7Ozs7OzBEQUNwRSw4REFBQ2I7Z0RBQUdWLFdBQVU7O2tFQUNWLDhEQUFDSzt3REFBS0wsV0FBVTtrRUFBMkRoQyxFQUFFd0QsYUFBYTs7Ozs7O2tFQUMxRiw4REFBQ25CO3dEQUFLTCxXQUFVO2tFQUEyQmhDLEVBQUU2QyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzdELDhEQUFDVjt3Q0FBRUgsV0FBVTtrREFBZ0VoQyxFQUFFeUQsWUFBWTs7Ozs7O2tEQUMzRiw4REFBQzNCO3dDQUFJRSxXQUFVO2tEQUNWbkIsU0FBU2tDLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxrQkFDakIsOERBQUNuQjtnREFBWUUsV0FBVTs7a0VBQ25CLDhEQUFDTzt3REFBSUMsS0FBSTt3REFBbUJDLEtBQUk7d0RBQUdULFdBQVU7Ozs7OztvREFDNUNnQjs7K0NBRktDOzs7Ozs7Ozs7O2tEQU1sQiw4REFBQ0M7d0NBQU9sQixXQUFVOzs0Q0FBb0toQyxFQUFFbUQsT0FBTzs0Q0FBQzswREFBQyw4REFBQ1o7Z0RBQUlDLEtBQUk7Z0RBQW1CQyxLQUFJO2dEQUFHVCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNMVAsOERBQUNGO29CQUFJRSxXQUFVOztzQ0FDWCw4REFBQ0Y7NEJBQUlFLFdBQVU7c0NBQ1gsNEVBQUNGO2dDQUFJRSxXQUFVOztrREFDWCw4REFBQ0Y7d0NBQUlFLFdBQVU7OzBEQUNYLDhEQUFDTztnREFBSUMsS0FBSTtnREFBb0JDLEtBQUk7Z0RBQUdULFdBQVU7Ozs7OzswREFDOUMsOERBQUNVO2dEQUFHVixXQUFVOzBEQUF3Q2hDLEVBQUUwRCxVQUFVOzs7Ozs7MERBQ2xFLDhEQUFDaEI7Z0RBQUdWLFdBQVU7O2tFQUNWLDhEQUFDSzt3REFBS0wsV0FBVTtrRUFBMkRoQyxFQUFFMkQsV0FBVzs7Ozs7O2tFQUN4Riw4REFBQ3RCO3dEQUFLTCxXQUFVO2tFQUEyQmhDLEVBQUU2QyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzdELDhEQUFDVjt3Q0FBRUgsV0FBVTtrREFBZ0VoQyxFQUFFNEQsVUFBVTs7Ozs7O2tEQUN6Riw4REFBQzlCO3dDQUFJRSxXQUFVO2tEQUNWWixPQUFPMkIsR0FBRyxDQUFDLENBQUNjLFNBQVNaLGtCQUNsQiw4REFBQ25CO2dEQUFZRSxXQUFVOztrRUFDbkIsOERBQUNPO3dEQUFJQyxLQUFJO3dEQUFtQkMsS0FBSTt3REFBR1QsV0FBVTs7Ozs7O29EQUM1QzZCOzsrQ0FGS1o7Ozs7Ozs7Ozs7a0RBTWxCLDhEQUFDQzt3Q0FBT2xCLFdBQVU7OzRDQUFvS2hDLEVBQUVtRCxPQUFPOzRDQUFDOzBEQUFDLDhEQUFDWjtnREFBSUMsS0FBSTtnREFBbUJDLEtBQUk7Z0RBQUdULFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUd0UCw4REFBQ0Y7NEJBQUlFLFdBQVU7c0NBQ1gsNEVBQUNGO2dDQUFJRSxXQUFVOztrREFDWCw4REFBQ0Y7d0NBQUlFLFdBQVU7OzBEQUNYLDhEQUFDTztnREFBSUMsS0FBSTtnREFBb0JDLEtBQUk7Z0RBQUdULFdBQVU7Ozs7OzswREFDOUMsOERBQUNVO2dEQUFHVixXQUFVOzBEQUF3Q2hDLEVBQUU4RCxTQUFTOzs7Ozs7MERBQ2pFLDhEQUFDcEI7Z0RBQUdWLFdBQVU7O2tFQUNWLDhEQUFDSzt3REFBS0wsV0FBVTtrRUFBMkRoQyxFQUFFK0QsVUFBVTs7Ozs7O2tFQUN2Riw4REFBQzFCO3dEQUFLTCxXQUFVO2tFQUEyQmhDLEVBQUU2QyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzdELDhEQUFDVjt3Q0FBRUgsV0FBVTtrREFBZ0VoQyxFQUFFZ0UsU0FBUzs7Ozs7O2tEQUN4Riw4REFBQ2xDO3dDQUFJRSxXQUFVO2tEQUNWUCxNQUFNc0IsR0FBRyxDQUFDLENBQUNjLFNBQVNaLGtCQUNqQiw4REFBQ25CO2dEQUFZRSxXQUFVOztrRUFDbkIsOERBQUNPO3dEQUFJQyxLQUFJO3dEQUFtQkMsS0FBSTt3REFBR1QsV0FBVTs7Ozs7O29EQUM1QzZCOzsrQ0FGS1o7Ozs7Ozs7Ozs7a0RBTWxCLDhEQUFDQzt3Q0FBT2xCLFdBQVU7OzRDQUFvS2hDLEVBQUVtRCxPQUFPOzRDQUFDOzBEQUFDLDhEQUFDWjtnREFBSUMsS0FBSTtnREFBbUJDLEtBQUk7Z0RBQUdULFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU0xUCw4REFBQ0Y7b0JBQUlFLFdBQVU7O3NDQUNYLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBbURoQyxFQUFFaUUsb0JBQW9COzs7Ozs7c0NBQ3ZGLDhEQUFDbkM7NEJBQUlFLFdBQVU7OzhDQUNYLDhEQUFDRjtvQ0FBSUUsV0FBVTs7c0RBQ1gsOERBQUNrQzs0Q0FBR2xDLFdBQVU7c0RBQXFDaEMsRUFBRW1FLFdBQVc7Ozs7OztzREFDaEUsOERBQUNoQzs0Q0FBRUgsV0FBVTtzREFBMENoQyxFQUFFb0UsWUFBWTs7Ozs7O3NEQUNyRSw4REFBQ2pDOzRDQUFFSCxXQUFVO3NEQUFzQmhDLEVBQUVxRSxlQUFlOzs7Ozs7Ozs7Ozs7OENBRXhELDhEQUFDdkM7b0NBQUlFLFdBQVU7O3NEQUNYLDhEQUFDa0M7NENBQUdsQyxXQUFVO3NEQUFxQ2hDLEVBQUVzRSxXQUFXOzs7Ozs7c0RBQ2hFLDhEQUFDbkM7NENBQUVILFdBQVU7c0RBQTBDaEMsRUFBRXVFLFlBQVk7Ozs7OztzREFDckUsOERBQUNwQzs0Q0FBRUgsV0FBVTtzREFBc0JoQyxFQUFFcUUsZUFBZTs7Ozs7Ozs7Ozs7OzhDQUV4RCw4REFBQ3ZDO29DQUFJRSxXQUFVOztzREFDWCw4REFBQ2tDOzRDQUFHbEMsV0FBVTtzREFBcUNoQyxFQUFFd0UsV0FBVzs7Ozs7O3NEQUNoRSw4REFBQ3JDOzRDQUFFSCxXQUFVO3NEQUEwQ2hDLEVBQUV5RSxZQUFZOzs7Ozs7c0RBQ3JFLDhEQUFDdEM7NENBQUVILFdBQVU7c0RBQXNCaEMsRUFBRTBFLGVBQWU7Ozs7Ozs7Ozs7Ozs4Q0FFeEQsOERBQUM1QztvQ0FBSUUsV0FBVTs7c0RBQ1gsOERBQUNrQzs0Q0FBR2xDLFdBQVU7c0RBQXFDaEMsRUFBRTJFLFdBQVc7Ozs7OztzREFDaEUsOERBQUN4Qzs0Q0FBRUgsV0FBVTtzREFBMENoQyxFQUFFNEUsWUFBWTs7Ozs7O3NEQUNyRSw4REFBQ3pDOzRDQUFFSCxXQUFVO3NEQUFzQmhDLEVBQUU2RSxZQUFZOzs7Ozs7Ozs7Ozs7OENBRXJELDhEQUFDL0M7b0NBQUlFLFdBQVU7O3NEQUNYLDhEQUFDa0M7NENBQUdsQyxXQUFVO3NEQUFxQ2hDLEVBQUU4RSxXQUFXOzs7Ozs7c0RBQ2hFLDhEQUFDM0M7NENBQUVILFdBQVU7c0RBQTBDaEMsRUFBRStFLFlBQVk7Ozs7OztzREFDckUsOERBQUM1Qzs0Q0FBRUgsV0FBVTtzREFBc0JoQyxFQUFFZ0YsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU05RCw4REFBQ2xEO29CQUFJRSxXQUFVOztzQ0FDWCw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQW1EaEMsRUFBRWlGLDJCQUEyQjs7Ozs7O3NDQUM5Riw4REFBQ25EOzRCQUFJRSxXQUFVOzs4Q0FDWCw4REFBQ0Y7b0NBQUlFLFdBQVU7O3NEQUNYLDhEQUFDa0M7NENBQUdsQyxXQUFVO3NEQUFxQ2hDLEVBQUVrRixnQkFBZ0I7Ozs7OztzREFDckUsOERBQUNwRDs0Q0FBSUUsV0FBVTs7OERBQ1gsOERBQUNLO29EQUFLTCxXQUFVOzhEQUFpQmhDLEVBQUVtRixhQUFhOzs7Ozs7OERBQ2hELDhEQUFDOUM7b0RBQUtMLFdBQVU7OERBQXdCbkMsdUVBQWtCQSxDQUFDLElBQUlFOzs7Ozs7Ozs7Ozs7c0RBRW5FLDhEQUFDK0I7NENBQUlFLFdBQVU7OzhEQUNYLDhEQUFDSztvREFBS0wsV0FBVTs4REFBaUJoQyxFQUFFb0YsV0FBVzs7Ozs7OzhEQUM5Qyw4REFBQy9DO29EQUFLTCxXQUFVOzhEQUF3Qm5DLHVFQUFrQkEsQ0FBQyxJQUFJRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUl2RSw4REFBQytCO29DQUFJRSxXQUFVOztzREFDWCw4REFBQ2tDOzRDQUFHbEMsV0FBVTtzREFBcUNoQyxFQUFFcUYsa0JBQWtCOzs7Ozs7c0RBQ3ZFLDhEQUFDdkQ7NENBQUlFLFdBQVU7OzhEQUNYLDhEQUFDSztvREFBS0wsV0FBVTs4REFBaUJoQyxFQUFFbUYsYUFBYTs7Ozs7OzhEQUNoRCw4REFBQzlDO29EQUFLTCxXQUFVOzhEQUF3Qm5DLHVFQUFrQkEsQ0FBQyxHQUFHRTs7Ozs7Ozs7Ozs7O3NEQUVsRSw4REFBQytCOzRDQUFJRSxXQUFVOzs4REFDWCw4REFBQ0s7b0RBQUtMLFdBQVU7OERBQWlCaEMsRUFBRW9GLFdBQVc7Ozs7Ozs4REFDOUMsOERBQUMvQztvREFBS0wsV0FBVTs4REFBd0JuQyx1RUFBa0JBLENBQUMsSUFBSUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJdkUsOERBQUMrQjtvQ0FBSUUsV0FBVTs7c0RBQ1gsOERBQUNrQzs0Q0FBR2xDLFdBQVU7c0RBQXFDaEMsRUFBRXNGLEtBQUs7Ozs7OztzREFDMUQsOERBQUN4RDs0Q0FBSUUsV0FBVTs7OERBQ1gsOERBQUNLO29EQUFLTCxXQUFVOzhEQUFpQmhDLEVBQUVtRixhQUFhOzs7Ozs7OERBQ2hELDhEQUFDOUM7b0RBQUtMLFdBQVU7OERBQXdCbkMsdUVBQWtCQSxDQUFDLEdBQUdFOzs7Ozs7Ozs7Ozs7c0RBRWxFLDhEQUFDK0I7NENBQUlFLFdBQVU7OzhEQUNYLDhEQUFDSztvREFBS0wsV0FBVTs4REFBaUJoQyxFQUFFb0YsV0FBVzs7Ozs7OzhEQUM5Qyw4REFBQy9DO29EQUFLTCxXQUFVOzhEQUF3Qm5DLHVFQUFrQkEsQ0FBQyxJQUFJRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUl2RSw4REFBQytCO29DQUFJRSxXQUFVOztzREFDWCw4REFBQ2tDOzRDQUFHbEMsV0FBVTtzREFBcUNoQyxFQUFFdUYsUUFBUTs7Ozs7O3NEQUM3RCw4REFBQ3pEOzRDQUFJRSxXQUFVOzs4REFDWCw4REFBQ0s7b0RBQUtMLFdBQVU7OERBQWlCaEMsRUFBRW1GLGFBQWE7Ozs7Ozs4REFDaEQsOERBQUM5QztvREFBS0wsV0FBVTs4REFBd0JuQyx1RUFBa0JBLENBQUMsR0FBR0U7Ozs7Ozs7Ozs7OztzREFFbEUsOERBQUMrQjs0Q0FBSUUsV0FBVTs7OERBQ1gsOERBQUNLO29EQUFLTCxXQUFVOzhEQUFpQmhDLEVBQUVvRixXQUFXOzs7Ozs7OERBQzlDLDhEQUFDL0M7b0RBQUtMLFdBQVU7OERBQXdCbkMsdUVBQWtCQSxDQUFDLElBQUlFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSXZFLDhEQUFDK0I7b0NBQUlFLFdBQVU7O3NEQUNYLDhEQUFDa0M7NENBQUdsQyxXQUFVO3NEQUFxQ2hDLEVBQUV3RixTQUFTOzs7Ozs7c0RBQzlELDhEQUFDMUQ7NENBQUlFLFdBQVU7OzhEQUNYLDhEQUFDSztvREFBS0wsV0FBVTs4REFBaUJoQyxFQUFFbUYsYUFBYTs7Ozs7OzhEQUNoRCw4REFBQzlDO29EQUFLTCxXQUFVOzhEQUF3Qm5DLHVFQUFrQkEsQ0FBQyxHQUFHRTs7Ozs7Ozs7Ozs7O3NEQUVsRSw4REFBQytCOzRDQUFJRSxXQUFVOzs4REFDWCw4REFBQ0s7b0RBQUtMLFdBQVU7OERBQWlCaEMsRUFBRW9GLFdBQVc7Ozs7Ozs4REFDOUMsOERBQUMvQztvREFBS0wsV0FBVTs4REFBd0JuQyx1RUFBa0JBLENBQUMsSUFBSUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJdkUsOERBQUMrQjtvQ0FBSUUsV0FBVTs7c0RBQ1gsOERBQUNrQzs0Q0FBR2xDLFdBQVU7c0RBQXFDaEMsRUFBRXlGLEtBQUs7Ozs7OztzREFDMUQsOERBQUMzRDs0Q0FBSUUsV0FBVTs7OERBQ1gsOERBQUNLO29EQUFLTCxXQUFVOzhEQUFpQmhDLEVBQUVtRixhQUFhOzs7Ozs7OERBQ2hELDhEQUFDOUM7b0RBQUtMLFdBQVU7OERBQXdCbkMsdUVBQWtCQSxDQUFDLEdBQUdFOzs7Ozs7Ozs7Ozs7c0RBRWxFLDhEQUFDK0I7NENBQUlFLFdBQVU7OzhEQUNYLDhEQUFDSztvREFBS0wsV0FBVTs4REFBaUJoQyxFQUFFb0YsV0FBVzs7Ozs7OzhEQUM5Qyw4REFBQy9DO29EQUFLTCxXQUFVOzhEQUF3Qm5DLHVFQUFrQkEsQ0FBQyxJQUFJRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU8vRSw4REFBQytCO29CQUFJRSxXQUFVOztzQ0FDWCw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQW1EaEMsRUFBRTBGLGNBQWM7Ozs7OztzQ0FDakYsOERBQUM1RDs0QkFBSUUsV0FBVTtzQ0FDWCw0RUFBQ0Y7Z0NBQUlFLFdBQVU7O2tEQUNYLDhEQUFDRjt3Q0FBSUUsV0FBVTs7MERBQ1gsOERBQUNrQztnREFBR2xDLFdBQVU7MERBQW9DOzs7Ozs7MERBQ2xELDhEQUFDRztnREFBRUgsV0FBVTswREFBb0M7Ozs7Ozs7Ozs7OztrREFFckQsOERBQUNGO3dDQUFJRSxXQUFVOzswREFDWCw4REFBQ2tDO2dEQUFHbEMsV0FBVTswREFBb0M7Ozs7OzswREFDbEQsOERBQUNHO2dEQUFFSCxXQUFVOzBEQUFvQzs7Ozs7Ozs7Ozs7O2tEQUVyRCw4REFBQ0Y7d0NBQUlFLFdBQVU7OzBEQUNYLDhEQUFDa0M7Z0RBQUdsQyxXQUFVOzBEQUFvQzs7Ozs7OzBEQUNsRCw4REFBQ0c7Z0RBQUVILFdBQVU7MERBQW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPakUsOERBQUNGO29CQUFJRSxXQUFVOztzQ0FDWCw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQW1EaEMsRUFBRTJGLG9CQUFvQjs7Ozs7O3NDQUN2Riw4REFBQzdEOzRCQUFJRSxXQUFVOzs4Q0FDWCw4REFBQ0Y7b0NBQUlFLFdBQVU7O3NEQUNYLDhEQUFDa0M7NENBQUdsQyxXQUFVO3NEQUFxQ2hDLEVBQUU0RixnQkFBZ0I7Ozs7OztzREFDckUsOERBQUN6RDs0Q0FBRUgsV0FBVTtzREFBbUJoQyxFQUFFNkYsb0JBQW9COzs7Ozs7c0RBQ3RELDhEQUFDL0Q7NENBQUlFLFdBQVU7OzhEQUNYLDhEQUFDSztvREFBS0wsV0FBVTs4REFBaUJoQyxFQUFFOEYsYUFBYTs7Ozs7OzhEQUNoRCw4REFBQ3pEO29EQUFLTCxXQUFVOzhEQUF3Qm5DLHVFQUFrQkEsQ0FBQyxLQUFLRTs7Ozs7Ozs7Ozs7O3NEQUVwRSw4REFBQ29DOzRDQUFFSCxXQUFVO3NEQUE4QmhDLEVBQUUrRix3QkFBd0I7Ozs7Ozs7Ozs7Ozs4Q0FHekUsOERBQUNqRTtvQ0FBSUUsV0FBVTs7c0RBQ1gsOERBQUNrQzs0Q0FBR2xDLFdBQVU7c0RBQXFDaEMsRUFBRWdHLHFCQUFxQjs7Ozs7O3NEQUMxRSw4REFBQzdEOzRDQUFFSCxXQUFVO3NEQUFtQmhDLEVBQUVpRyx5QkFBeUI7Ozs7OztzREFDM0QsOERBQUNuRTs0Q0FBSUUsV0FBVTs7OERBQ1gsOERBQUNLO29EQUFLTCxXQUFVOzhEQUFpQmhDLEVBQUVrRyxTQUFTOzs7Ozs7OERBQzVDLDhEQUFDN0Q7b0RBQUtMLFdBQVU7OERBQXdCbkMsdUVBQWtCQSxDQUFDLElBQUlFOzs7Ozs7Ozs7Ozs7c0RBRW5FLDhEQUFDb0M7NENBQUVILFdBQVU7c0RBQThCaEMsRUFBRW1HLGlDQUFpQzs7Ozs7Ozs7Ozs7OzhDQUdsRiw4REFBQ3JFO29DQUFJRSxXQUFVOztzREFDWCw4REFBQ2tDOzRDQUFHbEMsV0FBVTtzREFBcUNoQyxFQUFFb0csZ0JBQWdCOzs7Ozs7c0RBQ3JFLDhEQUFDakU7NENBQUVILFdBQVU7c0RBQW1CaEMsRUFBRXFHLG9CQUFvQjs7Ozs7O3NEQUN0RCw4REFBQ3ZFOzRDQUFJRSxXQUFVOzs4REFDWCw4REFBQ0s7b0RBQUtMLFdBQVU7OERBQWlCaEMsRUFBRXNHLFVBQVU7Ozs7Ozs4REFDN0MsOERBQUNqRTtvREFBS0wsV0FBVTs4REFBd0JuQyx1RUFBa0JBLENBQUMsSUFBSUU7Ozs7Ozs7Ozs7OztzREFFbkUsOERBQUNvQzs0Q0FBRUgsV0FBVTtzREFBOEJoQyxFQUFFdUcsc0JBQXNCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU8vRjtLQXZWTXpHO0FBeVZOLCtEQUFlQSxLQUFLQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvKHBhZ2VzKS9wcmljaW5nL1BsYW5zLmpzeD9jZTU5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IHRyYW5zbGF0aW9ucyB9IGZyb20gJ0AvYXBwL3RyYW5zbGF0aW9ucydcclxuaW1wb3J0IFByaWNpbmdDYWxjdWxhdG9yIGZyb20gJy4vUHJpY2luZ0NhbGN1bGF0b3InXHJcbmltcG9ydCB7IGZvcm1hdERpc3BsYXlQcmljZSB9IGZyb20gJ0AvYXBwL3V0aWxzL2N1cnJlbmN5J1xyXG5cclxuY29uc3QgUGxhbnMgPSAoeyBsb2NhbGUgPSAnZW4nIH0pID0+IHtcclxuICAgIGNvbnN0IHQgPSB0cmFuc2xhdGlvbnNbbG9jYWxlXSB8fCB0cmFuc2xhdGlvbnMuZW47XHJcbiAgICBcclxuICAgIGNvbnN0IGJhc2ljID0gW1xyXG4gICAgICAgIHQuYmFzaWNGZWF0dXJlMSxcclxuICAgICAgICB0LmJhc2ljRmVhdHVyZTIsXHJcbiAgICAgICAgdC5iYXNpY0ZlYXR1cmUzLFxyXG4gICAgICAgIHQuYmFzaWNGZWF0dXJlNFxyXG4gICAgXVxyXG4gICAgXHJcbiAgICBjb25zdCBwcmVtaXVtID0gW1xyXG4gICAgICAgIHQucHJlbWl1bUZlYXR1cmUxLFxyXG4gICAgICAgIHQucHJlbWl1bUZlYXR1cmUyLFxyXG4gICAgICAgIHQucHJlbWl1bUZlYXR1cmUzLFxyXG4gICAgICAgIHQucHJlbWl1bUZlYXR1cmU0LFxyXG4gICAgICAgIHQucHJlbWl1bUZlYXR1cmU1XHJcbiAgICBdXHJcbiAgICBcclxuICAgIGNvbnN0IHVsdGltYXRlID0gW1xyXG4gICAgICAgIHQudWx0aW1hdGVGZWF0dXJlMSxcclxuICAgICAgICB0LnVsdGltYXRlRmVhdHVyZTIsXHJcbiAgICAgICAgdC51bHRpbWF0ZUZlYXR1cmUzLFxyXG4gICAgICAgIHQudWx0aW1hdGVGZWF0dXJlNCxcclxuICAgICAgICB0LnVsdGltYXRlRmVhdHVyZTUsXHJcbiAgICAgICAgdC51bHRpbWF0ZUZlYXR1cmU2XHJcbiAgICBdXHJcbiAgICBcclxuICAgIGNvbnN0IGZhbWlseSA9IFtcclxuICAgICAgICB0LmZhbWlseUZlYXR1cmUxLFxyXG4gICAgICAgIHQuZmFtaWx5RmVhdHVyZTIsXHJcbiAgICAgICAgdC5mYW1pbHlGZWF0dXJlMyxcclxuICAgICAgICB0LmZhbWlseUZlYXR1cmU0XHJcbiAgICBdXHJcbiAgICBcclxuICAgIGNvbnN0IGdyb3VwID0gW1xyXG4gICAgICAgIHQuZ3JvdXBGZWF0dXJlMSxcclxuICAgICAgICB0Lmdyb3VwRmVhdHVyZTIsXHJcbiAgICAgICAgdC5ncm91cEZlYXR1cmUzLFxyXG4gICAgICAgIHQuZ3JvdXBGZWF0dXJlNFxyXG4gICAgXVxyXG4gICAgXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDxkaXYgaWQ9J2V2ZW50cycgY2xhc3NOYW1lPXtgdy1mdWxsIGgtZnVsbCBiZy1ibGFja2lzaGB9PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ndy1mdWxsIG14LWF1dG8gbWF4LXctWzE2MDBweF0gYm9yZGVyLXkgcHQtWzEwMHB4XSBwYi1bNTFweF0gZmxleC1jb2wgZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBtZDpweC0xMCBsZzpweC0xNiB4bDpweC0yMCAyeGw6cHgtNic+XHJcbiAgICAgICAgICAgICAgICB7LyogQWxsLUluY2x1c2l2ZSBQcmljaW5nIE1lc3NhZ2UgKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ndGV4dC1jZW50ZXIgbWItMTInPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9J3RleHQtWyNEQjFGRUJdIHRleHQtM3hsIGZvbnQtYm9sZCBtYi0yJz57dC5hbGxJbmNsdXNpdmVQcmljaW5nfTwvaDI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQteGwnPnt0LndoYXRZb3VTZWVJc1doYXRZb3VQYXl9PC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIHsvKiBJbnRlcmFjdGl2ZSBQcmljaW5nIENhbGN1bGF0b3IgKi99XHJcbiAgICAgICAgICAgICAgICA8UHJpY2luZ0NhbGN1bGF0b3IgbG9jYWxlPXtsb2NhbGV9IC8+XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdiZy1ncmFkaWVudC10by10ciBmcm9tLVsjOTI2QkI5XSB2aWEtWyM1QTc5RkJdIHRvLVsjMkZCQ0Y3XSByb3VuZGVkLXhsIHctWzMwM3B4XSBweC01IHB5LVsxNHB4XSBmbGV4IGdhcC0yIG10LTIwJz5cclxuICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPSdiZy13aGl0ZSB0ZXh0LVsyNnB4XSBmb250LXNlbWlib2xkIHB4LTggcHktNCByb3VuZGVkLXhsIHRleHQtZ3JhZG50Jz48c3BhbiBjbGFzc05hbWU9J3RleHQtZ3JhZGlhbnQnPnt0Lm1vbnRobHl9PC9zcGFuPiA8L2gyPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgeGw6Z3JpZC1jb2xzLTMgZ2FwLXktOCBtdC1bNjBweF0nPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdweC0yLjUnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ncm91bmRlZC1bMjBweF0gcC02IG1kOnB5LVszMXB4XSBtZDpweC1bMzNweF0gYm9yZGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdyZWxhdGl2ZSByb3VuZGVkLVsyMHB4XSBib3JkZXIgYm9yZGVyLVsjMjNBMUZGXSBmbGV4IGZsZXgtY29sIGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBweS1bMzBweF0gZHJvcC1zaGFkb3cteGwgc2hhZG93LVsjMjA5RkZGXSc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCIvYXNzZXRzL3NoZG93LnBuZ1wiIGFsdD1cIlwiIGNsYXNzTmFtZT0nYWJzb2x1dGUgdG9wLTAgaC1mdWxsIHotMCcgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQtWzI2cHhdIGZvbnQtc2VtaWJvbGQnPnt0LmJhc2ljUGxhbn08L2gxPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9J3RleHQtIHRleHQtWzI2cHhdICc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1bIzIzQTFGRl0gdGV4dC1bNDBweF0gbWQ6dGV4dC1bNTBweF0gZm9udC1zZW1pYm9sZCc+e3QuYmFzaWNQcmljZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LVsxOHB4XSAnPnt0LnBlck1vbnRofTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtbGcgdGV4dC13aGl0ZSBtdC1bMjhweF0gcGItWzI1cHhdIGxlYWRpbmctbm9uZSBib3JkZXItYic+e3QuYmFzaWNEZXNjfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtY29sIGdhcC0yLjUgbXQtWzM0cHhdJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YmFzaWMubWFwKChwbGFuLCBpKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyIGdhcC0xLjUgdGV4dC13aGl0ZSc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz1cIi9pY29ucy9jaGVjay5wbmdcIiBhbHQ9XCJcIiBjbGFzc05hbWU9JycgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwbGFufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9J3RleHQteGwgbXQtWzQzcHhdIGZvbnQtc2VtaWJvbGQgZmxleCBpdGVtcy1jZW50ZXIgcHktMiBtZDpweS00IHB4LTYgbWQ6cHgtOCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by10ciBmcm9tLVsjOTI2QkI5XSB2aWEtWyM1QTc5RkJdIHRvLVsjMkZCQ0Y3XSAnPnt0LmJvb2tOb3d9IDxpbWcgc3JjPVwiL2ljb25zL2Fycm93LnN2Z1wiIGFsdD1cIlwiIGNsYXNzTmFtZT0naC1bMjJweF0gdy1bMjJweF0gbWwtWzExcHhdIHJvdW5kZWQtZnVsbCcgLz48L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3B4LTIuNSc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdyb3VuZGVkLVsyMHB4XSBwLTYgbWQ6cHktWzMxcHhdIG1kOnB4LVszM3B4XSBib3JkZXInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3JlbGF0aXZlIHJvdW5kZWQtWzIwcHhdIGJvcmRlciBib3JkZXItWyMyM0ExRkZdIGZsZXggZmxleC1jb2wganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIHB5LVszMHB4XSBkcm9wLXNoYWRvdy14bCBzaGFkb3ctWyMyMDlGRkZdJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz1cIi9hc3NldHMvc2hkb3cucG5nXCIgYWx0PVwiXCIgY2xhc3NOYW1lPSdhYnNvbHV0ZSB0b3AtMCBoLWZ1bGwgei0wJyAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC1bMjZweF0gZm9udC1zZW1pYm9sZCc+e3QucHJlbWl1bVBsYW59PC9oMT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPSd0ZXh0LSB0ZXh0LVsyNnB4XSAnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3RleHQtWyMyM0ExRkZdIHRleHQtWzQwcHhdIG1kOnRleHQtWzUwcHhdIGZvbnQtc2VtaWJvbGQnPnt0LnByZW1pdW1QcmljZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LVsxOHB4XSAnPnt0LnBlck1vbnRofTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtbGcgdGV4dC13aGl0ZSBtdC1bMjhweF0gcGItWzI1cHhdIGxlYWRpbmctbm9uZSBib3JkZXItYic+e3QucHJlbWl1bURlc2N9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggZmxleC1jb2wgZ2FwLTIuNSBtdC1bMzRweF0nPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcmVtaXVtLm1hcCgocGxhbiwgaSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aX0gY2xhc3NOYW1lPSdmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMS41IHRleHQtd2hpdGUnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCIvaWNvbnMvY2hlY2sucG5nXCIgYWx0PVwiXCIgY2xhc3NOYW1lPScnIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cGxhbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPSd0ZXh0LXhsIG10LVs0M3B4XSBmb250LXNlbWlib2xkIGZsZXggaXRlbXMtY2VudGVyIHB5LTIgbWQ6cHktNCBweC02IG1kOnB4LTggdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgYmctZ3JhZGllbnQtdG8tdHIgZnJvbS1bIzkyNkJCOV0gdmlhLVsjNUE3OUZCXSB0by1bIzJGQkNGN10gJz57dC5ib29rTm93fSA8aW1nIHNyYz1cIi9pY29ucy9hcnJvdy5zdmdcIiBhbHQ9XCJcIiBjbGFzc05hbWU9J2gtWzIycHhdIHctWzIycHhdIG1sLVsxMXB4XSByb3VuZGVkLWZ1bGwnIC8+PC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdweC0yLjUnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ncm91bmRlZC1bMjBweF0gcC02IG1kOnB5LVszMXB4XSBtZDpweC1bMzNweF0gYm9yZGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdyZWxhdGl2ZSByb3VuZGVkLVsyMHB4XSBib3JkZXIgYm9yZGVyLVsjMjNBMUZGXSBmbGV4IGZsZXgtY29sIGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBweS1bMzBweF0gZHJvcC1zaGFkb3cteGwgc2hhZG93LVsjMjA5RkZGXSc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCIvYXNzZXRzL3NoZG93LnBuZ1wiIGFsdD1cIlwiIGNsYXNzTmFtZT0nYWJzb2x1dGUgdG9wLTAgaC1mdWxsIHotMCcgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQtWzI2cHhdIGZvbnQtc2VtaWJvbGQnPnt0LnVsdGltYXRlUGxhbn08L2gxPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9J3RleHQtIHRleHQtWzI2cHhdICc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1bIzIzQTFGRl0gdGV4dC1bNDBweF0gbWQ6dGV4dC1bNTBweF0gZm9udC1zZW1pYm9sZCc+e3QudWx0aW1hdGVQcmljZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LVsxOHB4XSAnPnt0LnBlck1vbnRofTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtbGcgdGV4dC13aGl0ZSBtdC1bMjhweF0gcGItWzI1cHhdIGxlYWRpbmctbm9uZSBib3JkZXItYic+e3QudWx0aW1hdGVEZXNjfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtY29sIGdhcC0yLjUgbXQtWzM0cHhdJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dWx0aW1hdGUubWFwKChwbGFuLCBpKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyIGdhcC0xLjUgdGV4dC13aGl0ZSc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz1cIi9pY29ucy9jaGVjay5wbmdcIiBhbHQ9XCJcIiBjbGFzc05hbWU9JycgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwbGFufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9J3RleHQteGwgbXQtWzQzcHhdIGZvbnQtc2VtaWJvbGQgZmxleCBpdGVtcy1jZW50ZXIgcHktMiBtZDpweS00IHB4LTYgbWQ6cHgtOCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by10ciBmcm9tLVsjOTI2QkI5XSB2aWEtWyM1QTc5RkJdIHRvLVsjMkZCQ0Y3XSAnPnt0LmJvb2tOb3d9IDxpbWcgc3JjPVwiL2ljb25zL2Fycm93LnN2Z1wiIGFsdD1cIlwiIGNsYXNzTmFtZT0naC1bMjJweF0gdy1bMjJweF0gbWwtWzExcHhdIHJvdW5kZWQtZnVsbCcgLz48L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgey8qIEZhbWlseSBhbmQgR3JvdXAgUGxhbnMgKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtOCB3LWZ1bGwgbXQtMjAnPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdweC0yLjUnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ncm91bmRlZC1bMjBweF0gcC02IG1kOnB5LVszMXB4XSBtZDpweC1bMzNweF0gYm9yZGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdyZWxhdGl2ZSByb3VuZGVkLVsyMHB4XSBib3JkZXIgYm9yZGVyLVsjMjNBMUZGXSBmbGV4IGZsZXgtY29sIGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBweS1bMzBweF0gZHJvcC1zaGFkb3cteGwgc2hhZG93LVsjMjA5RkZGXSc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCIvYXNzZXRzL3NoZG93LnBuZ1wiIGFsdD1cIlwiIGNsYXNzTmFtZT0nYWJzb2x1dGUgdG9wLTAgaC1mdWxsIHotMCcgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQtWzI2cHhdIGZvbnQtc2VtaWJvbGQnPnt0LmZhbWlseVBsYW59PC9oMT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPSd0ZXh0LSB0ZXh0LVsyNnB4XSAnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3RleHQtWyMyM0ExRkZdIHRleHQtWzQwcHhdIG1kOnRleHQtWzUwcHhdIGZvbnQtc2VtaWJvbGQnPnt0LmZhbWlseVByaWNlfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQtWzE4cHhdICc+e3QucGVyTW9udGh9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDE+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT0ndGV4dC1sZyB0ZXh0LXdoaXRlIG10LVsyOHB4XSBwYi1bMjVweF0gbGVhZGluZy1ub25lIGJvcmRlci1iJz57dC5mYW1pbHlEZXNjfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtY29sIGdhcC0yLjUgbXQtWzM0cHhdJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmFtaWx5Lm1hcCgoZmVhdHVyZSwgaSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aX0gY2xhc3NOYW1lPSdmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMS41IHRleHQtd2hpdGUnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCIvaWNvbnMvY2hlY2sucG5nXCIgYWx0PVwiXCIgY2xhc3NOYW1lPScnIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPSd0ZXh0LXhsIG10LVs0M3B4XSBmb250LXNlbWlib2xkIGZsZXggaXRlbXMtY2VudGVyIHB5LTIgbWQ6cHktNCBweC02IG1kOnB4LTggdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgYmctZ3JhZGllbnQtdG8tdHIgZnJvbS1bIzkyNkJCOV0gdmlhLVsjNUE3OUZCXSB0by1bIzJGQkNGN10gJz57dC5ib29rTm93fSA8aW1nIHNyYz1cIi9pY29ucy9hcnJvdy5zdmdcIiBhbHQ9XCJcIiBjbGFzc05hbWU9J2gtWzIycHhdIHctWzIycHhdIG1sLVsxMXB4XSByb3VuZGVkLWZ1bGwnIC8+PC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdweC0yLjUnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ncm91bmRlZC1bMjBweF0gcC02IG1kOnB5LVszMXB4XSBtZDpweC1bMzNweF0gYm9yZGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdyZWxhdGl2ZSByb3VuZGVkLVsyMHB4XSBib3JkZXIgYm9yZGVyLVsjMjNBMUZGXSBmbGV4IGZsZXgtY29sIGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBweS1bMzBweF0gZHJvcC1zaGFkb3cteGwgc2hhZG93LVsjMjA5RkZGXSc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCIvYXNzZXRzL3NoZG93LnBuZ1wiIGFsdD1cIlwiIGNsYXNzTmFtZT0nYWJzb2x1dGUgdG9wLTAgaC1mdWxsIHotMCcgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQtWzI2cHhdIGZvbnQtc2VtaWJvbGQnPnt0Lmdyb3VwUGxhbn08L2gxPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9J3RleHQtIHRleHQtWzI2cHhdICc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1bIzIzQTFGRl0gdGV4dC1bNDBweF0gbWQ6dGV4dC1bNTBweF0gZm9udC1zZW1pYm9sZCc+e3QuZ3JvdXBQcmljZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LVsxOHB4XSAnPnt0LnBlck1vbnRofTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtbGcgdGV4dC13aGl0ZSBtdC1bMjhweF0gcGItWzI1cHhdIGxlYWRpbmctbm9uZSBib3JkZXItYic+e3QuZ3JvdXBEZXNjfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtY29sIGdhcC0yLjUgbXQtWzM0cHhdJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z3JvdXAubWFwKChmZWF0dXJlLCBpKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyIGdhcC0xLjUgdGV4dC13aGl0ZSc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz1cIi9pY29ucy9jaGVjay5wbmdcIiBhbHQ9XCJcIiBjbGFzc05hbWU9JycgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmZWF0dXJlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9J3RleHQteGwgbXQtWzQzcHhdIGZvbnQtc2VtaWJvbGQgZmxleCBpdGVtcy1jZW50ZXIgcHktMiBtZDpweS00IHB4LTYgbWQ6cHgtOCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by10ciBmcm9tLVsjOTI2QkI5XSB2aWEtWyM1QTc5RkJdIHRvLVsjMkZCQ0Y3XSAnPnt0LmJvb2tOb3d9IDxpbWcgc3JjPVwiL2ljb25zL2Fycm93LnN2Z1wiIGFsdD1cIlwiIGNsYXNzTmFtZT0naC1bMjJweF0gdy1bMjJweF0gbWwtWzExcHhdIHJvdW5kZWQtZnVsbCcgLz48L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgey8qIEhvdXJseSAmIERhaWx5IFBhc3NlcyAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSd3LWZ1bGwgbXQtMjAnPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC0zeGwgZm9udC1ib2xkIHRleHQtY2VudGVyIG1iLTEwJz57dC5ob3VybHlBbmREYWlseVBhc3Nlc308L2gyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIHhsOmdyaWQtY29scy01IGdhcC02Jz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2JnLWdyYXktOTAwIHJvdW5kZWQteGwgcC02IHRleHQtY2VudGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGQgbWItMic+e3Qub25lSG91clBhc3N9PC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT0ndGV4dC1bIzIzQTFGRl0gdGV4dC0zeGwgZm9udC1ib2xkIG1iLTEnPnt0Lm9uZUhvdXJQcmljZX08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC1zbSc+e3QudW5saW1pdGVkQWNjZXNzfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdiZy1ncmF5LTkwMCByb3VuZGVkLXhsIHAtNiB0ZXh0LWNlbnRlcic+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQteGwgZm9udC1ib2xkIG1iLTInPnt0LnR3b0hvdXJQYXNzfTwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtWyMyM0ExRkZdIHRleHQtM3hsIGZvbnQtYm9sZCBtYi0xJz57dC50d29Ib3VyUHJpY2V9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQtc20nPnt0LnVubGltaXRlZEFjY2Vzc308L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nYmctZ3JheS05MDAgcm91bmRlZC14bCBwLTYgdGV4dC1jZW50ZXInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LXhsIGZvbnQtYm9sZCBtYi0yJz57dC5oYWxmRGF5UGFzc308L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPSd0ZXh0LVsjMjNBMUZGXSB0ZXh0LTN4bCBmb250LWJvbGQgbWItMSc+e3QuaGFsZkRheVByaWNlfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LXNtJz57dC5mb3VySG91cnNBY2Nlc3N9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2JnLWdyYXktOTAwIHJvdW5kZWQteGwgcC02IHRleHQtY2VudGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGQgbWItMic+e3QuZnVsbERheVBhc3N9PC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT0ndGV4dC1bIzIzQTFGRl0gdGV4dC0zeGwgZm9udC1ib2xkIG1iLTEnPnt0LmZ1bGxEYXlQcmljZX08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC1zbSc+e3QuYWxsRGF5QWNjZXNzfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdiZy1ncmF5LTkwMCByb3VuZGVkLXhsIHAtNiB0ZXh0LWNlbnRlcic+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQteGwgZm9udC1ib2xkIG1iLTInPnt0LndlZWtlbmRQYXNzfTwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtWyMyM0ExRkZdIHRleHQtM3hsIGZvbnQtYm9sZCBtYi0xJz57dC53ZWVrZW5kUHJpY2V9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQtc20nPnt0LndlZWtlbmRBY2Nlc3N9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICB7LyogSW5kaXZpZHVhbCBFeHBlcmllbmNlIFByaWNpbmcgKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ndy1mdWxsIG10LTIwJz5cclxuICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWNlbnRlciBtYi0xMCc+e3QuaW5kaXZpZHVhbEV4cGVyaWVuY2VQcmljaW5nfTwvaDI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTgnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nYmctZ3JheS05MDAgcm91bmRlZC14bCBwLTYnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LXhsIGZvbnQtYm9sZCBtYi00Jz57dC5mcmVlUm9hbWluZ0FyZW5hfTwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGJvcmRlci1iIGJvcmRlci1ncmF5LTcwMCBwYi0yIG1iLTInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1ncmF5LTMwMCc+e3Quc2luZ2xlU2Vzc2lvbn08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSdmb250LWJvbGQgdGV4dC13aGl0ZSc+e2Zvcm1hdERpc3BsYXlQcmljZSgxMiwgbG9jYWxlKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1ncmF5LTMwMCc+e3QudHdvU2Vzc2lvbnN9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nZm9udC1ib2xkIHRleHQtd2hpdGUnPntmb3JtYXREaXNwbGF5UHJpY2UoMjAsIGxvY2FsZSl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2JnLWdyYXktOTAwIHJvdW5kZWQteGwgcC02Jz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGQgbWItNCc+e3QudWZvU3BhY2VzaGlwQ2luZW1hfTwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGJvcmRlci1iIGJvcmRlci1ncmF5LTcwMCBwYi0yIG1iLTInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1ncmF5LTMwMCc+e3Quc2luZ2xlU2Vzc2lvbn08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSdmb250LWJvbGQgdGV4dC13aGl0ZSc+e2Zvcm1hdERpc3BsYXlQcmljZSg5LCBsb2NhbGUpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlcic+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSd0ZXh0LWdyYXktMzAwJz57dC50d29TZXNzaW9uc308L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSdmb250LWJvbGQgdGV4dC13aGl0ZSc+e2Zvcm1hdERpc3BsYXlQcmljZSgxNSwgbG9jYWxlKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nYmctZ3JheS05MDAgcm91bmRlZC14bCBwLTYnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LXhsIGZvbnQtYm9sZCBtYi00Jz57dC52cjM2MH08L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBib3JkZXItYiBib3JkZXItZ3JheS03MDAgcGItMiBtYi0yJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3RleHQtZ3JheS0zMDAnPnt0LnNpbmdsZVNlc3Npb259PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nZm9udC1ib2xkIHRleHQtd2hpdGUnPntmb3JtYXREaXNwbGF5UHJpY2UoOSwgbG9jYWxlKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1ncmF5LTMwMCc+e3QudHdvU2Vzc2lvbnN9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nZm9udC1ib2xkIHRleHQtd2hpdGUnPntmb3JtYXREaXNwbGF5UHJpY2UoMTUsIGxvY2FsZSl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2JnLWdyYXktOTAwIHJvdW5kZWQteGwgcC02Jz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGQgbWItNCc+e3QudnJCYXR0bGV9PC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgYm9yZGVyLWIgYm9yZGVyLWdyYXktNzAwIHBiLTIgbWItMic+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSd0ZXh0LWdyYXktMzAwJz57dC5zaW5nbGVTZXNzaW9ufTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J2ZvbnQtYm9sZCB0ZXh0LXdoaXRlJz57Zm9ybWF0RGlzcGxheVByaWNlKDksIGxvY2FsZSl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3RleHQtZ3JheS0zMDAnPnt0LnR3b1Nlc3Npb25zfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J2ZvbnQtYm9sZCB0ZXh0LXdoaXRlJz57Zm9ybWF0RGlzcGxheVByaWNlKDE1LCBsb2NhbGUpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdiZy1ncmF5LTkwMCByb3VuZGVkLXhsIHAtNic+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQteGwgZm9udC1ib2xkIG1iLTQnPnt0LnZyV2Fycmlvcn08L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBib3JkZXItYiBib3JkZXItZ3JheS03MDAgcGItMiBtYi0yJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3RleHQtZ3JheS0zMDAnPnt0LnNpbmdsZVNlc3Npb259PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nZm9udC1ib2xkIHRleHQtd2hpdGUnPntmb3JtYXREaXNwbGF5UHJpY2UoNywgbG9jYWxlKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1ncmF5LTMwMCc+e3QudHdvU2Vzc2lvbnN9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nZm9udC1ib2xkIHRleHQtd2hpdGUnPntmb3JtYXREaXNwbGF5UHJpY2UoMTIsIGxvY2FsZSl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2JnLWdyYXktOTAwIHJvdW5kZWQteGwgcC02Jz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGQgbWItNCc+e3QudnJDYXR9PC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgYm9yZGVyLWIgYm9yZGVyLWdyYXktNzAwIHBiLTIgbWItMic+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSd0ZXh0LWdyYXktMzAwJz57dC5zaW5nbGVTZXNzaW9ufTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J2ZvbnQtYm9sZCB0ZXh0LXdoaXRlJz57Zm9ybWF0RGlzcGxheVByaWNlKDYsIGxvY2FsZSl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3RleHQtZ3JheS0zMDAnPnt0LnR3b1Nlc3Npb25zfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J2ZvbnQtYm9sZCB0ZXh0LXdoaXRlJz57Zm9ybWF0RGlzcGxheVByaWNlKDEwLCBsb2NhbGUpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICB7LyogR3JvdXAgRGlzY291bnRzICovfVxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3ctZnVsbCBtdC0yMCc+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1jZW50ZXIgbWItMTAnPnt0Lmdyb3VwRGlzY291bnRzfTwvaDI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2JnLWdyYXktOTAwIHJvdW5kZWQteGwgcC04Jz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTgnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3RleHQtY2VudGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQteGwgZm9udC1ib2xkIG1iLTMnPjUtOSBQZW9wbGU8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT0ndGV4dC1bIzIzQTFGRl0gdGV4dC0zeGwgZm9udC1ib2xkJz4xMCUgT0ZGPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ndGV4dC1jZW50ZXInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGQgbWItMyc+MTAtMTkgUGVvcGxlPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtWyMyM0ExRkZdIHRleHQtM3hsIGZvbnQtYm9sZCc+MTUlIE9GRjwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3RleHQtY2VudGVyJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHRleHQteGwgZm9udC1ib2xkIG1iLTMnPjIwKyBQZW9wbGU8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT0ndGV4dC1bIzIzQTFGRl0gdGV4dC0zeGwgZm9udC1ib2xkJz4yMCUgT0ZGPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIHsvKiBTcGVjaWFsIEV2ZW50cyAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSd3LWZ1bGwgbXQtMjAnPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC0zeGwgZm9udC1ib2xkIHRleHQtY2VudGVyIG1iLTEwJz57dC5zcGVjaWFsRXZlbnRQYWNrYWdlc308L2gyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC04Jz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2JnLWdyYXktOTAwIHJvdW5kZWQteGwgcC02Jz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGQgbWItNCc+e3QuYmlydGhkYXlQYWNrYWdlc308L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIG1iLTQnPnt0LmJpcnRoZGF5UGFja2FnZXNEZXNjfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgYm9yZGVyLWIgYm9yZGVyLWdyYXktNzAwIHBiLTIgbWItMic+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSd0ZXh0LWdyYXktMzAwJz57dC5zdGFydGluZ1ByaWNlfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J2ZvbnQtYm9sZCB0ZXh0LXdoaXRlJz57Zm9ybWF0RGlzcGxheVByaWNlKDI0OSwgbG9jYWxlKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT0ndGV4dC1zbSB0ZXh0LWdyYXktNDAwIG10LTInPnt0LmJpcnRoZGF5UGFja2FnZXNJbmNsdWRlc308L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2JnLWdyYXktOTAwIHJvdW5kZWQteGwgcC02Jz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J3RleHQtd2hpdGUgdGV4dC14bCBmb250LWJvbGQgbWItNCc+e3QuY29ycG9yYXRlVGVhbUJ1aWxkaW5nfTwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtd2hpdGUgbWItNCc+e3QuY29ycG9yYXRlVGVhbUJ1aWxkaW5nRGVzY308L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGJvcmRlci1iIGJvcmRlci1ncmF5LTcwMCBwYi0yIG1iLTInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1ncmF5LTMwMCc+e3QucGVyUGVyc29ufTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J2ZvbnQtYm9sZCB0ZXh0LXdoaXRlJz57Zm9ybWF0RGlzcGxheVByaWNlKDQ1LCBsb2NhbGUpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPSd0ZXh0LXNtIHRleHQtZ3JheS00MDAgbXQtMic+e3QuY29ycG9yYXRlVGVhbUJ1aWxkaW5nQ3VzdG9taXphYmxlfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nYmctZ3JheS05MDAgcm91bmRlZC14bCBwLTYnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT0ndGV4dC13aGl0ZSB0ZXh0LXhsIGZvbnQtYm9sZCBtYi00Jz57dC5zY2hvb2xGaWVsZFRyaXBzfTwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtd2hpdGUgbWItNCc+e3Quc2Nob29sRmllbGRUcmlwc0Rlc2N9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBib3JkZXItYiBib3JkZXItZ3JheS03MDAgcGItMiBtYi0yJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3RleHQtZ3JheS0zMDAnPnt0LnBlclN0dWRlbnR9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nZm9udC1ib2xkIHRleHQtd2hpdGUnPntmb3JtYXREaXNwbGF5UHJpY2UoMjUsIGxvY2FsZSl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtc20gdGV4dC1ncmF5LTQwMCBtdC0yJz57dC50ZWFjaGVyQ2hhcGVyb25lUGFzc2VzfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IFBsYW5zXHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInRyYW5zbGF0aW9ucyIsIlByaWNpbmdDYWxjdWxhdG9yIiwiZm9ybWF0RGlzcGxheVByaWNlIiwiUGxhbnMiLCJsb2NhbGUiLCJ0IiwiZW4iLCJiYXNpYyIsImJhc2ljRmVhdHVyZTEiLCJiYXNpY0ZlYXR1cmUyIiwiYmFzaWNGZWF0dXJlMyIsImJhc2ljRmVhdHVyZTQiLCJwcmVtaXVtIiwicHJlbWl1bUZlYXR1cmUxIiwicHJlbWl1bUZlYXR1cmUyIiwicHJlbWl1bUZlYXR1cmUzIiwicHJlbWl1bUZlYXR1cmU0IiwicHJlbWl1bUZlYXR1cmU1IiwidWx0aW1hdGUiLCJ1bHRpbWF0ZUZlYXR1cmUxIiwidWx0aW1hdGVGZWF0dXJlMiIsInVsdGltYXRlRmVhdHVyZTMiLCJ1bHRpbWF0ZUZlYXR1cmU0IiwidWx0aW1hdGVGZWF0dXJlNSIsInVsdGltYXRlRmVhdHVyZTYiLCJmYW1pbHkiLCJmYW1pbHlGZWF0dXJlMSIsImZhbWlseUZlYXR1cmUyIiwiZmFtaWx5RmVhdHVyZTMiLCJmYW1pbHlGZWF0dXJlNCIsImdyb3VwIiwiZ3JvdXBGZWF0dXJlMSIsImdyb3VwRmVhdHVyZTIiLCJncm91cEZlYXR1cmUzIiwiZ3JvdXBGZWF0dXJlNCIsImRpdiIsImlkIiwiY2xhc3NOYW1lIiwiaDIiLCJhbGxJbmNsdXNpdmVQcmljaW5nIiwicCIsIndoYXRZb3VTZWVJc1doYXRZb3VQYXkiLCJzcGFuIiwibW9udGhseSIsImltZyIsInNyYyIsImFsdCIsImgxIiwiYmFzaWNQbGFuIiwiYmFzaWNQcmljZSIsInBlck1vbnRoIiwiYmFzaWNEZXNjIiwibWFwIiwicGxhbiIsImkiLCJidXR0b24iLCJib29rTm93IiwicHJlbWl1bVBsYW4iLCJwcmVtaXVtUHJpY2UiLCJwcmVtaXVtRGVzYyIsInVsdGltYXRlUGxhbiIsInVsdGltYXRlUHJpY2UiLCJ1bHRpbWF0ZURlc2MiLCJmYW1pbHlQbGFuIiwiZmFtaWx5UHJpY2UiLCJmYW1pbHlEZXNjIiwiZmVhdHVyZSIsImdyb3VwUGxhbiIsImdyb3VwUHJpY2UiLCJncm91cERlc2MiLCJob3VybHlBbmREYWlseVBhc3NlcyIsImgzIiwib25lSG91clBhc3MiLCJvbmVIb3VyUHJpY2UiLCJ1bmxpbWl0ZWRBY2Nlc3MiLCJ0d29Ib3VyUGFzcyIsInR3b0hvdXJQcmljZSIsImhhbGZEYXlQYXNzIiwiaGFsZkRheVByaWNlIiwiZm91ckhvdXJzQWNjZXNzIiwiZnVsbERheVBhc3MiLCJmdWxsRGF5UHJpY2UiLCJhbGxEYXlBY2Nlc3MiLCJ3ZWVrZW5kUGFzcyIsIndlZWtlbmRQcmljZSIsIndlZWtlbmRBY2Nlc3MiLCJpbmRpdmlkdWFsRXhwZXJpZW5jZVByaWNpbmciLCJmcmVlUm9hbWluZ0FyZW5hIiwic2luZ2xlU2Vzc2lvbiIsInR3b1Nlc3Npb25zIiwidWZvU3BhY2VzaGlwQ2luZW1hIiwidnIzNjAiLCJ2ckJhdHRsZSIsInZyV2FycmlvciIsInZyQ2F0IiwiZ3JvdXBEaXNjb3VudHMiLCJzcGVjaWFsRXZlbnRQYWNrYWdlcyIsImJpcnRoZGF5UGFja2FnZXMiLCJiaXJ0aGRheVBhY2thZ2VzRGVzYyIsInN0YXJ0aW5nUHJpY2UiLCJiaXJ0aGRheVBhY2thZ2VzSW5jbHVkZXMiLCJjb3Jwb3JhdGVUZWFtQnVpbGRpbmciLCJjb3Jwb3JhdGVUZWFtQnVpbGRpbmdEZXNjIiwicGVyUGVyc29uIiwiY29ycG9yYXRlVGVhbUJ1aWxkaW5nQ3VzdG9taXphYmxlIiwic2Nob29sRmllbGRUcmlwcyIsInNjaG9vbEZpZWxkVHJpcHNEZXNjIiwicGVyU3R1ZGVudCIsInRlYWNoZXJDaGFwZXJvbmVQYXNzZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(pages)/pricing/Plans.jsx\n"));

/***/ })

});