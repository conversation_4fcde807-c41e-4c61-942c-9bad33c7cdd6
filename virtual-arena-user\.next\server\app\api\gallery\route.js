"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/gallery/route";
exports.ids = ["app/api/gallery/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgallery%2Froute&page=%2Fapi%2Fgallery%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgallery%2Froute.js&appDir=C%3A%5CUsers%5CRS%20ENTERPRISE%5CDocuments%5Coffice%5Cvrtual-arena%5Cvirtual-arena-user%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRS%20ENTERPRISE%5CDocuments%5Coffice%5Cvrtual-arena%5Cvirtual-arena-user&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgallery%2Froute&page=%2Fapi%2Fgallery%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgallery%2Froute.js&appDir=C%3A%5CUsers%5CRS%20ENTERPRISE%5CDocuments%5Coffice%5Cvrtual-arena%5Cvirtual-arena-user%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRS%20ENTERPRISE%5CDocuments%5Coffice%5Cvrtual-arena%5Cvirtual-arena-user&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_RS_ENTERPRISE_Documents_office_vrtual_arena_virtual_arena_user_src_app_api_gallery_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/gallery/route.js */ \"(rsc)/./src/app/api/gallery/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/gallery/route\",\n        pathname: \"/api/gallery\",\n        filename: \"route\",\n        bundlePath: \"app/api/gallery/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\office\\\\vrtual-arena\\\\virtual-arena-user\\\\src\\\\app\\\\api\\\\gallery\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_RS_ENTERPRISE_Documents_office_vrtual_arena_virtual_arena_user_src_app_api_gallery_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/gallery/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgallery%2Froute&page=%2Fapi%2Fgallery%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgallery%2Froute.js&appDir=C%3A%5CUsers%5CRS%20ENTERPRISE%5CDocuments%5Coffice%5Cvrtual-arena%5Cvirtual-arena-user%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRS%20ENTERPRISE%5CDocuments%5Coffice%5Cvrtual-arena%5Cvirtual-arena-user&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/gallery/route.js":
/*!**************************************!*\
  !*** ./src/app/api/gallery/route.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Helper function to get media base URL\nfunction getMediaBaseUrl() {\n    // For deployment, use the environment variable or construct from request\n    if (process.env.NEXT_PUBLIC_MEDIA_BASE_URL) {\n        return process.env.NEXT_PUBLIC_MEDIA_BASE_URL.replace(/\\/$/, \"\");\n    }\n    // For development, use localhost\n    if (true) {\n        return \"http://localhost:3000\";\n    }\n    // For production, try to get from headers or use relative paths\n    return \"\";\n}\nasync function GET(request) {\n    try {\n        const galleryDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"public\", \"gallery\");\n        const mediaBaseUrl = getMediaBaseUrl();\n        // Check if directory exists\n        try {\n            await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(galleryDir);\n        } catch  {\n            // Directory doesn't exist, return empty arrays\n            return Response.json({\n                images: [],\n                videos: [],\n                mediaBaseUrl\n            });\n        }\n        // Read the directory\n        const files = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readdir(galleryDir);\n        // Filter and sort images\n        const images = files.filter((file)=>/\\.(jpg|jpeg|png|gif|webp)$/i.test(file)).sort().map((img)=>({\n                filename: img,\n                url: mediaBaseUrl ? `${mediaBaseUrl}/gallery/${img}` : `/gallery/${img}`,\n                type: \"image\"\n            }));\n        // Filter and sort videos with poster images\n        const videos = files.filter((file)=>/\\.(mp4|webm|ogg)$/i.test(file)).sort().map((vid)=>{\n            const posterName = vid.replace(/\\.(mp4|webm|ogg)$/i, \".jpg\");\n            const posterExists = files.includes(posterName);\n            return {\n                filename: vid,\n                url: mediaBaseUrl ? `${mediaBaseUrl}/gallery/${vid}` : `/gallery/${vid}`,\n                poster: posterExists ? mediaBaseUrl ? `${mediaBaseUrl}/gallery/${posterName}` : `/gallery/${posterName}` : null,\n                type: \"video\"\n            };\n        });\n        return Response.json({\n            images,\n            videos,\n            mediaBaseUrl,\n            total: images.length + videos.length\n        });\n    } catch (error) {\n        console.error(\"Gallery fetch error:\", error);\n        return Response.json({\n            images: [],\n            videos: [],\n            mediaBaseUrl: getMediaBaseUrl(),\n            error: \"Failed to fetch gallery content\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/gallery/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgallery%2Froute&page=%2Fapi%2Fgallery%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgallery%2Froute.js&appDir=C%3A%5CUsers%5CRS%20ENTERPRISE%5CDocuments%5Coffice%5Cvrtual-arena%5Cvirtual-arena-user%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRS%20ENTERPRISE%5CDocuments%5Coffice%5Cvrtual-arena%5Cvirtual-arena-user&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();