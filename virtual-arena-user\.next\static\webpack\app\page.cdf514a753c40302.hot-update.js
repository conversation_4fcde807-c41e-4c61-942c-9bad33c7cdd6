"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/ApiUrl.js":
/*!*****************************!*\
  !*** ./src/utils/ApiUrl.js ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_URL: function() { return /* binding */ API_URL; },\n/* harmony export */   SOCKET_URL: function() { return /* binding */ SOCKET_URL; },\n/* harmony export */   checkMediaExists: function() { return /* binding */ checkMediaExists; },\n/* harmony export */   getAuthHeaders: function() { return /* binding */ getAuthHeaders; },\n/* harmony export */   getFullMediaUrl: function() { return /* binding */ getFullMediaUrl; },\n/* harmony export */   getMediaBaseUrl: function() { return /* binding */ getMediaBaseUrl; },\n/* harmony export */   getOptimizedImageUrl: function() { return /* binding */ getOptimizedImageUrl; },\n/* harmony export */   getPaymentApiUrl: function() { return /* binding */ getPaymentApiUrl; },\n/* harmony export */   validateToken: function() { return /* binding */ validateToken; }\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n// src/constants.js or wherever this file is\n// Use relative URLs that will be proxied by Nginx\nconst API_URL = \"/api/v1\"; // this is for the production\n// export const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1';\n// Helper function to get payment API URL\nconst getPaymentApiUrl = ()=>{\n    // Since API_URL is always '/api/v1' in production and 'http://localhost:8080/api/v1' in development\n    // We can simply append '/payment' to get the payment endpoint\n    return \"\".concat(API_URL, \"/payment\");\n};\n// Helper function to get the base URL for media/assets\nconst getMediaBaseUrl = ()=>{\n    // 1️⃣ Prefer an explicit env variable (e.g. NEXT_PUBLIC_MEDIA_BASE_URL)\n    if (process.env.NEXT_PUBLIC_MEDIA_BASE_URL) {\n        return process.env.NEXT_PUBLIC_MEDIA_BASE_URL.replace(/\\/$/, \"\");\n    }\n    // 2️⃣ If running in the browser pick the current origin (avoids mixed-content in prod)\n    if ( true && window.location) {\n        const origin = window.location.origin;\n        // In development the frontend usually runs on localhost:3000 while the backend is 8080\n        // Development: if site is served from localhost:3000 or 127.x.x.x:3000 or any host:3000, map to backend 8080 where images reside\n        const devBackend = origin.replace(\":3000\", \":8080\");\n        if (origin.includes(\"localhost\")) {\n            return \"http://localhost:8080\";\n        }\n        if (origin.endsWith(\":3000\")) {\n            return devBackend;\n        }\n        return origin; // e.g. https://vrtualarena.ca\n    }\n    // 3️⃣ Fallback: derive host from API_URL\n    try {\n        const url = new URL(API_URL);\n        return \"\".concat(url.protocol, \"//\").concat(url.hostname).concat(url.port ? \":\".concat(url.port) : \"\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst SOCKET_URL = \"/socket\";\nconst getAuthHeaders = ()=>{\n    const authToken = localStorage.getItem(\"token\");\n    // Debug: Log token status\n    if (!authToken) {\n        console.error(\"Authentication token not found in localStorage\");\n        return {\n            headers: {}\n        };\n    }\n    // Debug: Log token format and validate\n    console.log(\"Token found:\", authToken.substring(0, 20) + \"...\");\n    // The backend expects the token with 'Bearer ' prefix\n    // Looking at authMiddleware.js, it uses token.replace('Bearer ', '') to remove the prefix\n    // So we need to ensure the token has the Bearer prefix\n    const formattedToken = authToken.startsWith(\"Bearer \") ? authToken : \"Bearer \".concat(authToken);\n    console.log(\"Formatted token:\", formattedToken.substring(0, 27) + \"...\");\n    return {\n        headers: {\n            Authorization: formattedToken\n        }\n    };\n};\n// Add a function to validate the token by making a test request\nconst validateToken = async ()=>{\n    try {\n        const authToken = localStorage.getItem(\"token\");\n        if (!authToken) {\n            console.error(\"No token found to validate\");\n            return false;\n        }\n        // Format token with Bearer prefix if needed\n        const formattedToken = authToken.startsWith(\"Bearer \") ? authToken : \"Bearer \".concat(authToken);\n        // Make a test request to an endpoint that requires authentication\n        const response = await fetch(\"\".concat(API_URL, \"/auth/\"), {\n            method: \"GET\",\n            headers: {\n                Authorization: formattedToken\n            }\n        });\n        if (response.ok) {\n            console.log(\"Token is valid\");\n            return true;\n        } else {\n            console.error(\"Token validation failed:\", response.status, response.statusText);\n            return false;\n        }\n    } catch (error) {\n        console.error(\"Error validating token:\", error);\n        return false;\n    }\n};\n// Helper function to get full media URL\nconst getFullMediaUrl = (relativePath)=>{\n    if (!relativePath) return \"\";\n    // If already a full URL, return as is\n    if (relativePath.startsWith(\"http://\") || relativePath.startsWith(\"https://\")) {\n        return relativePath;\n    }\n    const baseUrl = getMediaBaseUrl();\n    const cleanPath = relativePath.startsWith(\"/\") ? relativePath : \"/\".concat(relativePath);\n    return baseUrl ? \"\".concat(baseUrl).concat(cleanPath) : cleanPath;\n};\n// Helper function to check if media file exists\nconst checkMediaExists = async (url)=>{\n    try {\n        const response = await fetch(url, {\n            method: \"HEAD\"\n        });\n        return response.ok;\n    } catch (e) {\n        return false;\n    }\n};\n// Helper function to get optimized image URL\nconst getOptimizedImageUrl = function(src) {\n    let width = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 800, quality = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 75;\n    if (!src) return \"\";\n    // If it's already optimized or external, return as is\n    if (src.includes(\"/_next/image\") || src.startsWith(\"http\")) {\n        return src;\n    }\n    const baseUrl = getMediaBaseUrl();\n    const params = new URLSearchParams({\n        url: src.startsWith(\"/\") ? src : \"/\".concat(src),\n        w: width.toString(),\n        q: quality.toString()\n    });\n    return \"\".concat(baseUrl, \"/_next/image?\").concat(params.toString());\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/ApiUrl.js\n"));

/***/ })

});