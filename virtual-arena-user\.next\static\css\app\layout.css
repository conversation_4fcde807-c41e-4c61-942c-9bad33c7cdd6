/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.container{
  width: 100%;
}
@media (min-width: 640px){

  .container{
    max-width: 640px;
  }
}
@media (min-width: 768px){

  .container{
    max-width: 768px;
  }
}
@media (min-width: 1024px){

  .container{
    max-width: 1024px;
  }
}
@media (min-width: 1280px){

  .container{
    max-width: 1280px;
  }
}
@media (min-width: 1536px){

  .container{
    max-width: 1536px;
  }
}
.pointer-events-none{
  pointer-events: none;
}
.pointer-events-auto{
  pointer-events: auto;
}
.visible{
  visibility: visible;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.inset-0{
  inset: 0px;
}
.-right-1{
  right: -0.25rem;
}
.-right-2{
  right: -0.5rem;
}
.-top-1{
  top: -0.25rem;
}
.-top-2{
  top: -0.5rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-20{
  bottom: 5rem;
}
.bottom-4{
  bottom: 1rem;
}
.left-0{
  left: 0px;
}
.left-4{
  left: 1rem;
}
.left-6{
  left: 1.5rem;
}
.right-0{
  right: 0px;
}
.right-4{
  right: 1rem;
}
.right-6{
  right: 1.5rem;
}
.right-\[5\%\]{
  right: 5%;
}
.top-0{
  top: 0px;
}
.top-24{
  top: 6rem;
}
.top-3{
  top: 0.75rem;
}
.top-4{
  top: 1rem;
}
.top-5{
  top: 1.25rem;
}
.top-6{
  top: 1.5rem;
}
.top-\[52px\]{
  top: 52px;
}
.top-full{
  top: 100%;
}
.z-0{
  z-index: 0;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-30{
  z-index: 30;
}
.z-40{
  z-index: 40;
}
.z-50{
  z-index: 50;
}
.z-\[1001\]{
  z-index: 1001;
}
.col-span-1{
  grid-column: span 1 / span 1;
}
.col-span-2{
  grid-column: span 2 / span 2;
}
.col-span-5{
  grid-column: span 5 / span 5;
}
.row-span-2{
  grid-row: span 2 / span 2;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-3{
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-8{
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.my-auto{
  margin-top: auto;
  margin-bottom: auto;
}
.-ml-20{
  margin-left: -5rem;
}
.-mt-\[90px\]{
  margin-top: -90px;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-10{
  margin-bottom: 2.5rem;
}
.mb-11{
  margin-bottom: 2.75rem;
}
.mb-12{
  margin-bottom: 3rem;
}
.mb-16{
  margin-bottom: 4rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-5{
  margin-bottom: 1.25rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-7{
  margin-bottom: 1.75rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.mb-\[41px\]{
  margin-bottom: 41px;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-1\.5{
  margin-left: 0.375rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-4{
  margin-left: 1rem;
}
.ml-\[11px\]{
  margin-left: 11px;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.mr-4{
  margin-right: 1rem;
}
.mt-0\.5{
  margin-top: 0.125rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-10{
  margin-top: 2.5rem;
}
.mt-12{
  margin-top: 3rem;
}
.mt-14{
  margin-top: 3.5rem;
}
.mt-16{
  margin-top: 4rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-2\.5{
  margin-top: 0.625rem;
}
.mt-20{
  margin-top: 5rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-7{
  margin-top: 1.75rem;
}
.mt-8{
  margin-top: 2rem;
}
.mt-\[20px\]{
  margin-top: 20px;
}
.mt-\[22px\]{
  margin-top: 22px;
}
.mt-\[28px\]{
  margin-top: 28px;
}
.mt-\[34px\]{
  margin-top: 34px;
}
.mt-\[36px\]{
  margin-top: 36px;
}
.mt-\[37px\]{
  margin-top: 37px;
}
.mt-\[43px\]{
  margin-top: 43px;
}
.mt-\[45px\]{
  margin-top: 45px;
}
.mt-\[51px\]{
  margin-top: 51px;
}
.mt-\[60px\]{
  margin-top: 60px;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.hidden{
  display: none;
}
.aspect-video{
  aspect-ratio: 16 / 9;
}
.h-10{
  height: 2.5rem;
}
.h-11{
  height: 2.75rem;
}
.h-12{
  height: 3rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-20{
  height: 5rem;
}
.h-24{
  height: 6rem;
}
.h-3{
  height: 0.75rem;
}
.h-4{
  height: 1rem;
}
.h-40{
  height: 10rem;
}
.h-48{
  height: 12rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-8{
  height: 2rem;
}
.h-\[100px\]{
  height: 100px;
}
.h-\[101px\]{
  height: 101px;
}
.h-\[14px\]{
  height: 14px;
}
.h-\[16px\]{
  height: 16px;
}
.h-\[18px\]{
  height: 18px;
}
.h-\[197px\]{
  height: 197px;
}
.h-\[200px\]{
  height: 200px;
}
.h-\[220px\]{
  height: 220px;
}
.h-\[22px\]{
  height: 22px;
}
.h-\[260px\]{
  height: 260px;
}
.h-\[300px\]{
  height: 300px;
}
.h-\[315px\]{
  height: 315px;
}
.h-\[390px\]{
  height: 390px;
}
.h-\[400px\]{
  height: 400px;
}
.h-\[40px\]{
  height: 40px;
}
.h-\[50px\]{
  height: 50px;
}
.h-\[50vh\]{
  height: 50vh;
}
.h-\[60vh\]{
  height: 60vh;
}
.h-\[700px\]{
  height: 700px;
}
.h-\[70vh\]{
  height: 70vh;
}
.h-\[85px\]{
  height: 85px;
}
.h-auto{
  height: auto;
}
.h-fit{
  height: -moz-fit-content;
  height: fit-content;
}
.h-full{
  height: 100%;
}
.h-screen{
  height: 100vh;
}
.max-h-60{
  max-height: 15rem;
}
.max-h-\[547px\]{
  max-height: 547px;
}
.max-h-\[811px\]{
  max-height: 811px;
}
.max-h-\[90vh\]{
  max-height: 90vh;
}
.min-h-\[220px\]{
  min-height: 220px;
}
.min-h-\[400px\]{
  min-height: 400px;
}
.min-h-\[450px\]{
  min-height: 450px;
}
.min-h-\[570px\]{
  min-height: 570px;
}
.min-h-\[600px\]{
  min-height: 600px;
}
.min-h-\[60vh\]{
  min-height: 60vh;
}
.min-h-full{
  min-height: 100%;
}
.min-h-screen{
  min-height: 100vh;
}
.w-1\/2{
  width: 50%;
}
.w-10{
  width: 2.5rem;
}
.w-12{
  width: 3rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-20{
  width: 5rem;
}
.w-24{
  width: 6rem;
}
.w-28{
  width: 7rem;
}
.w-3{
  width: 0.75rem;
}
.w-4{
  width: 1rem;
}
.w-48{
  width: 12rem;
}
.w-5{
  width: 1.25rem;
}
.w-52{
  width: 13rem;
}
.w-6{
  width: 1.5rem;
}
.w-64{
  width: 16rem;
}
.w-72{
  width: 18rem;
}
.w-8{
  width: 2rem;
}
.w-80{
  width: 20rem;
}
.w-\[120px\]{
  width: 120px;
}
.w-\[140px\]{
  width: 140px;
}
.w-\[14px\]{
  width: 14px;
}
.w-\[16px\]{
  width: 16px;
}
.w-\[179px\]{
  width: 179px;
}
.w-\[18px\]{
  width: 18px;
}
.w-\[199px\]{
  width: 199px;
}
.w-\[20\%\]{
  width: 20%;
}
.w-\[22px\]{
  width: 22px;
}
.w-\[303px\]{
  width: 303px;
}
.w-\[50px\]{
  width: 50px;
}
.w-\[60px\]{
  width: 60px;
}
.w-\[72px\]{
  width: 72px;
}
.w-\[78\%\]{
  width: 78%;
}
.w-auto{
  width: auto;
}
.w-fit{
  width: -moz-fit-content;
  width: fit-content;
}
.w-full{
  width: 100%;
}
.w-screen{
  width: 100vw;
}
.min-w-0{
  min-width: 0px;
}
.min-w-3{
  min-width: 0.75rem;
}
.min-w-\[100px\]{
  min-width: 100px;
}
.min-w-fit{
  min-width: -moz-fit-content;
  min-width: fit-content;
}
.min-w-full{
  min-width: 100%;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-3xl{
  max-width: 48rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-5xl{
  max-width: 64rem;
}
.max-w-7xl{
  max-width: 80rem;
}
.max-w-\[1087px\]{
  max-width: 1087px;
}
.max-w-\[130px\]{
  max-width: 130px;
}
.max-w-\[1440px\]{
  max-width: 1440px;
}
.max-w-\[1600px\]{
  max-width: 1600px;
}
.max-w-\[240px\]{
  max-width: 240px;
}
.max-w-\[300px\]{
  max-width: 300px;
}
.max-w-\[347px\]{
  max-width: 347px;
}
.max-w-\[358px\]{
  max-width: 358px;
}
.max-w-\[422px\]{
  max-width: 422px;
}
.max-w-\[540px\]{
  max-width: 540px;
}
.max-w-\[600px\]{
  max-width: 600px;
}
.max-w-\[700px\]{
  max-width: 700px;
}
.max-w-\[720px\]{
  max-width: 720px;
}
.max-w-\[858px\]{
  max-width: 858px;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-xl{
  max-width: 36rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.flex-grow{
  flex-grow: 1;
}
.origin-top-right{
  transform-origin: top right;
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full{
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0{
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse{

  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{

  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-default{
  cursor: default;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
.cursor-pointer{
  cursor: pointer;
}
.resize-none{
  resize: none;
}
.list-inside{
  list-style-position: inside;
}
.list-disc{
  list-style-type: disc;
}
.appearance-none{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-flow-col{
  grid-auto-flow: column;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-7{
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.grid-rows-2{
  grid-template-rows: repeat(2, minmax(0, 1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-col{
  flex-direction: column;
}
.flex-wrap{
  flex-wrap: wrap;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-1{
  gap: 0.25rem;
}
.gap-1\.5{
  gap: 0.375rem;
}
.gap-10{
  gap: 2.5rem;
}
.gap-12{
  gap: 3rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-2\.5{
  gap: 0.625rem;
}
.gap-20{
  gap: 5rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-5{
  gap: 1.25rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-8{
  gap: 2rem;
}
.gap-\[30px\]{
  gap: 30px;
}
.gap-x-5{
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
}
.gap-y-10{
  row-gap: 2.5rem;
}
.gap-y-6{
  row-gap: 1.5rem;
}
.gap-y-8{
  row-gap: 2rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.self-end{
  align-self: flex-end;
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.overflow-x-hidden{
  overflow-x: hidden;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-normal{
  white-space: normal;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-wrap{
  white-space: pre-wrap;
}
.text-wrap{
  text-wrap: wrap;
}
.text-nowrap{
  text-wrap: nowrap;
}
.break-words{
  overflow-wrap: break-word;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-\[20px\]{
  border-radius: 20px;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: 0.5rem;
}
.rounded-md{
  border-radius: 0.375rem;
}
.rounded-xl{
  border-radius: 0.75rem;
}
.border{
  border-width: 1px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-y{
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-l{
  border-left-width: 1px;
}
.border-r{
  border-right-width: 1px;
}
.border-r-2{
  border-right-width: 2px;
}
.border-t{
  border-top-width: 1px;
}
.border-t-0{
  border-top-width: 0px;
}
.border-t-2{
  border-top-width: 2px;
}
.border-\[\#23A1FF\]{
  --tw-border-opacity: 1;
  border-color: rgb(35 161 255 / var(--tw-border-opacity, 1));
}
.border-\[\#926BB9\]{
  --tw-border-opacity: 1;
  border-color: rgb(146 107 185 / var(--tw-border-opacity, 1));
}
.border-\[\#DB1FEB\]{
  --tw-border-opacity: 1;
  border-color: rgb(219 31 235 / var(--tw-border-opacity, 1));
}
.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-600{
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.border-gray-700{
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.border-gray-800{
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}
.border-green-200{
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-500{
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-green-500\/30{
  border-color: rgb(34 197 94 / 0.3);
}
.border-purple-400{
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.border-purple-500{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.border-purple-500\/30{
  border-color: rgb(168 85 247 / 0.3);
}
.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-red-500\/30{
  border-color: rgb(239 68 68 / 0.3);
}
.border-transparent{
  border-color: transparent;
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-yellow-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-t-purple-500{
  --tw-border-opacity: 1;
  border-top-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.bg-\[\#0C1339\]{
  --tw-bg-opacity: 1;
  background-color: rgb(12 19 57 / var(--tw-bg-opacity, 1));
}
.bg-\[\#0b1739\]{
  --tw-bg-opacity: 1;
  background-color: rgb(11 23 57 / var(--tw-bg-opacity, 1));
}
.bg-\[\#121212\]{
  --tw-bg-opacity: 1;
  background-color: rgb(18 18 18 / var(--tw-bg-opacity, 1));
}
.bg-\[\#1E1E1E\]{
  --tw-bg-opacity: 1;
  background-color: rgb(30 30 30 / var(--tw-bg-opacity, 1));
}
.bg-\[\#23A1FF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(35 161 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#2A2A2A\]{
  --tw-bg-opacity: 1;
  background-color: rgb(42 42 42 / var(--tw-bg-opacity, 1));
}
.bg-\[\#47B0FF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(71 176 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#5A79FB\]{
  --tw-bg-opacity: 1;
  background-color: rgb(90 121 251 / var(--tw-bg-opacity, 1));
}
.bg-\[\#5b2493\]{
  --tw-bg-opacity: 1;
  background-color: rgb(91 36 147 / var(--tw-bg-opacity, 1));
}
.bg-\[\#DB1FEB\]{
  --tw-bg-opacity: 1;
  background-color: rgb(219 31 235 / var(--tw-bg-opacity, 1));
}
.bg-\[rgba\(0\2c 0\2c 0\2c 0\.2\)\]{
  background-color: rgba(0,0,0,0.2);
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/50{
  background-color: rgb(0 0 0 / 0.5);
}
.bg-blackish{
  --tw-bg-opacity: 1;
  background-color: rgb(16 16 16 / var(--tw-bg-opacity, 1));
}
.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-blue-700{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-300{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-600{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.bg-gray-700{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.bg-gray-800{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-900{
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-600{
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-green-600\/20{
  background-color: rgb(22 163 74 / 0.2);
}
.bg-green-900{
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}
.bg-purple-600{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-purple-700{
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}
.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-600{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-red-600\/20{
  background-color: rgb(220 38 38 / 0.2);
}
.bg-red-900\/20{
  background-color: rgb(127 29 29 / 0.2);
}
.bg-transparent{
  background-color: transparent;
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-yellow-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}
.bg-opacity-60{
  --tw-bg-opacity: 0.6;
}
.bg-opacity-70{
  --tw-bg-opacity: 0.7;
}
.bg-opacity-90{
  --tw-bg-opacity: 0.9;
}
.bg-opacity-95{
  --tw-bg-opacity: 0.95;
}
.bg-cardbg{
  background-image: url('/assets/card.png');
}
.bg-contactbg{
  background-image: url('/assets/contactbg.png');
}
.bg-dealbg{
  background-image: url('/assets/dealbg.png');
}
.bg-gradient-to-b{
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-tr{
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}
.bg-herobg{
  background-image: url('/assets/herobg.jpg');
}
.bg-offer2{
  background-image: url('/assets/offer2.png');
}
.bg-offer3{
  background-image: url('/assets/offer3.jpg');
}
.bg-package{
  background-image: url('/assets/package.png');
}
.bg-pricingbg{
  background-image: url('/assets/pricingbg.png');
}
.from-\[\#00000000\]{
  --tw-gradient-from: #00000000 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#003C66\]{
  --tw-gradient-from: #003C66 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 60 102 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#023B6299\]{
  --tw-gradient-from: #023B6299 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(2 59 98 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#1E1E1E\]{
  --tw-gradient-from: #1E1E1E var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 30 30 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#1a1a1a\]{
  --tw-gradient-from: #1a1a1a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 26 26 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#926BB9\]{
  --tw-gradient-from: #926BB9 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(146 107 185 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#DB1FEB\]{
  --tw-gradient-from: #DB1FEB var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 31 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black{
  --tw-gradient-from: #000 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/70{
  --tw-gradient-from: rgb(0 0 0 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600{
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-700{
  --tw-gradient-from: #374151 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(55 65 81 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-900{
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/20{
  --tw-gradient-from: rgb(34 197 94 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-600{
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-500{
  --tw-gradient-from: #ec4899 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600\/20{
  --tw-gradient-from: rgb(147 51 234 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-900\/50{
  --tw-gradient-from: rgb(88 28 135 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500\/20{
  --tw-gradient-from: rgb(239 68 68 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent{
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-\[\#5A79FB\]{
  --tw-gradient-to: rgb(90 121 251 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #5A79FB var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gray-900{
  --tw-gradient-to: rgb(17 24 39 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #111827 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-900{
  --tw-gradient-to: rgb(88 28 135 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #581c87 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-\[\#\#580060\]{
  --tw-gradient-to: ##580060 var(--tw-gradient-to-position);
}
.to-\[\#00000080\]{
  --tw-gradient-to: #00000080 var(--tw-gradient-to-position);
}
.to-\[\#24CBFF\]{
  --tw-gradient-to: #24CBFF var(--tw-gradient-to-position);
}
.to-\[\#2D2D2D\]{
  --tw-gradient-to: #2D2D2D var(--tw-gradient-to-position);
}
.to-\[\#2FBCF7\]{
  --tw-gradient-to: #2FBCF7 var(--tw-gradient-to-position);
}
.to-\[\#49094F66\]{
  --tw-gradient-to: #49094F66 var(--tw-gradient-to-position);
}
.to-\[\#580060\]{
  --tw-gradient-to: #580060 var(--tw-gradient-to-position);
}
.to-\[\#7721F3\]{
  --tw-gradient-to: #7721F3 var(--tw-gradient-to-position);
}
.to-black{
  --tw-gradient-to: #000 var(--tw-gradient-to-position);
}
.to-blue-600{
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-emerald-500\/20{
  --tw-gradient-to: rgb(16 185 129 / 0.2) var(--tw-gradient-to-position);
}
.to-gray-800{
  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);
}
.to-gray-900{
  --tw-gradient-to: #111827 var(--tw-gradient-to-position);
}
.to-pink-500\/20{
  --tw-gradient-to: rgb(236 72 153 / 0.2) var(--tw-gradient-to-position);
}
.to-pink-600\/20{
  --tw-gradient-to: rgb(219 39 119 / 0.2) var(--tw-gradient-to-position);
}
.to-pink-900\/50{
  --tw-gradient-to: rgb(131 24 67 / 0.5) var(--tw-gradient-to-position);
}
.to-purple-600{
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-teal-600{
  --tw-gradient-to: #0d9488 var(--tw-gradient-to-position);
}
.to-violet-900{
  --tw-gradient-to: #4c1d95 var(--tw-gradient-to-position);
}
.bg-cover{
  background-size: cover;
}
.bg-center{
  background-position: center;
}
.bg-no-repeat{
  background-repeat: no-repeat;
}
.object-contain{
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}
.object-center{
  -o-object-position: center;
     object-position: center;
}
.p-1{
  padding: 0.25rem;
}
.p-2{
  padding: 0.5rem;
}
.p-2\.5{
  padding: 0.625rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-5{
  padding: 1.25rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.p-\[14px\]{
  padding: 14px;
}
.p-\[27px\]{
  padding: 27px;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-12{
  padding-left: 3rem;
  padding-right: 3rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-\[28px\]{
  padding-left: 28px;
  padding-right: 28px;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-\[100px\]{
  padding-top: 100px;
  padding-bottom: 100px;
}
.py-\[14px\]{
  padding-top: 14px;
  padding-bottom: 14px;
}
.py-\[20px\]{
  padding-top: 20px;
  padding-bottom: 20px;
}
.py-\[22px\]{
  padding-top: 22px;
  padding-bottom: 22px;
}
.py-\[27px\]{
  padding-top: 27px;
  padding-bottom: 27px;
}
.py-\[30px\]{
  padding-top: 30px;
  padding-bottom: 30px;
}
.py-\[35px\]{
  padding-top: 35px;
  padding-bottom: 35px;
}
.py-\[50px\]{
  padding-top: 50px;
  padding-bottom: 50px;
}
.pb-12{
  padding-bottom: 3rem;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-5{
  padding-bottom: 1.25rem;
}
.pb-8{
  padding-bottom: 2rem;
}
.pb-\[100px\]{
  padding-bottom: 100px;
}
.pb-\[15px\]{
  padding-bottom: 15px;
}
.pb-\[18px\]{
  padding-bottom: 18px;
}
.pb-\[25px\]{
  padding-bottom: 25px;
}
.pb-\[51px\]{
  padding-bottom: 51px;
}
.pb-\[59px\]{
  padding-bottom: 59px;
}
.pb-\[88px\]{
  padding-bottom: 88px;
}
.pl-2{
  padding-left: 0.5rem;
}
.pl-4{
  padding-left: 1rem;
}
.pr-10{
  padding-right: 2.5rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pt-16{
  padding-top: 4rem;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-5{
  padding-top: 1.25rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pt-\[100px\]{
  padding-top: 100px;
}
.pt-\[113px\]{
  padding-top: 113px;
}
.pt-\[120px\]{
  padding-top: 120px;
}
.pt-\[15px\]{
  padding-top: 15px;
}
.pt-\[91px\]{
  padding-top: 91px;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.text-start{
  text-align: start;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-6xl{
  font-size: 3.75rem;
  line-height: 1;
}
.text-\[12px\]{
  font-size: 12px;
}
.text-\[14px\]{
  font-size: 14px;
}
.text-\[16px\]{
  font-size: 16px;
}
.text-\[18px\]{
  font-size: 18px;
}
.text-\[20px\]{
  font-size: 20px;
}
.text-\[22px\]{
  font-size: 22px;
}
.text-\[24px\]{
  font-size: 24px;
}
.text-\[26px\]{
  font-size: 26px;
}
.text-\[30px\]{
  font-size: 30px;
}
.text-\[35px\]{
  font-size: 35px;
}
.text-\[40px\]{
  font-size: 40px;
}
.text-\[45px\]{
  font-size: 45px;
}
.text-\[50px\]{
  font-size: 50px;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-extrabold{
  font-weight: 800;
}
.font-light{
  font-weight: 300;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.capitalize{
  text-transform: capitalize;
}
.italic{
  font-style: italic;
}
.leading-none{
  line-height: 1;
}
.leading-tight{
  line-height: 1.25;
}
.tracking-tight{
  letter-spacing: -0.025em;
}
.tracking-wider{
  letter-spacing: 0.05em;
}
.text-\[\#23A1FF\]{
  --tw-text-opacity: 1;
  color: rgb(35 161 255 / var(--tw-text-opacity, 1));
}
.text-\[\#DB1FEB\]{
  --tw-text-opacity: 1;
  color: rgb(219 31 235 / var(--tw-text-opacity, 1));
}
.text-\[\#FFA600\]{
  --tw-text-opacity: 1;
  color: rgb(255 166 0 / var(--tw-text-opacity, 1));
}
.text-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-gray-200{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-green-300{
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-700{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-purple-400{
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-400{
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-400{
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline{
  text-decoration-line: underline;
}
.line-through{
  text-decoration-line: line-through;
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-20{
  opacity: 0.2;
}
.opacity-30{
  opacity: 0.3;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-80{
  opacity: 0.8;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[\#209FFF\]{
  --tw-shadow-color: #209FFF;
  --tw-shadow: var(--tw-shadow-colored);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.ring-1{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-black{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}
.ring-opacity-5{
  --tw-ring-opacity: 0.05;
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-\[0_0_10px_rgba\(255\2c 255\2c 255\2c 0\.7\)\]{
  --tw-drop-shadow: drop-shadow(0 0 10px rgba(255,255,255,0.7));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-xl{
  --tw-drop-shadow: drop-shadow(0 20px 13px rgb(0 0 0 / 0.03)) drop-shadow(0 8px 5px rgb(0 0 0 / 0.08));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow{
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-100{
  transition-duration: 100ms;
}
.duration-1000{
  transition-duration: 1000ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-500{
  transition-duration: 500ms;
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.bg-grad{
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
  --tw-gradient-from: #926BB9 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(146 107 185 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(90 121 251 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #5A79FB var(--tw-gradient-via-position), var(--tw-gradient-to);
  --tw-gradient-to: #2FBCF7 var(--tw-gradient-to-position);
}

/* Prevent horizontal scrolling */
html, body {
  overflow-x: hidden;
  max-width: 100%;
  position: relative;
}

/* Fix for French text that might be longer */
.text-wrap-balance {
  text-wrap: balance;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
          hyphens: auto;
}

/* Responsive text classes */
@media (max-width: 640px) {
  .responsive-text-sm {
    font-size: 0.875rem; /* 14px */
  }
  .responsive-text-md {
    font-size: 1rem; /* 16px */
  }
  .responsive-text-lg {
    font-size: 1.125rem; /* 18px */
  }
  
  /* French text adjustments */
  html[lang="fr"] .text-wrap-balance {
    font-size: 0.9em;
  }
}

/* Navbar specific fixes for mobile */
@media (max-width: 1024px) {
  .navbar-container {
    width: 100%;
    overflow-x: hidden;
  }
  
  .navbar-button {
    font-size: 0.875rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  /* Reduce button text size in French */
  html[lang="fr"] .btn-text {
    font-size: 0.875rem;
  }
}

/* Component specific fixes for French */
@media (max-width: 768px) {
  html[lang="fr"] .long-text-container {
    max-width: 100%;
    overflow-wrap: break-word;
  }
  
  html[lang="fr"] .btn-text {
    font-size: 0.75rem;
  }
  
  /* Adjust plan features for French */
  html[lang="fr"] #plans .text-wrap-balance {
    font-size: 0.9em;
  }

  /* Gallery, Events, and Testimonials sections for French */
  html[lang="fr"] #gallery .text-wrap-balance,
  html[lang="fr"] #events .text-wrap-balance,
  html[lang="fr"] #testimonials .text-wrap-balance {
    font-size: 0.9em;
    line-height: 1.4;
  }

  /* Ensure buttons don't overflow on mobile */
  .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* French button text adjustments */
  html[lang="fr"] .btn-text {
    font-size: 0.75rem;
    white-space: normal;
    text-align: center;
  }
}

/* Additional mobile responsiveness for very small screens */
@media (max-width: 480px) {
  /* Ensure headings don't overflow */
  h1, h2, h3 {
    word-break: break-word;
    -webkit-hyphens: auto;
            hyphens: auto;
  }

  /* French text specific adjustments for very small screens */
  html[lang="fr"] h1 {
    font-size: 0.9em;
    line-height: 1.2;
  }

  html[lang="fr"] h2 {
    font-size: 0.95em;
    line-height: 1.3;
  }

  /* Ensure contact information doesn't overflow */
  .contact-info {
    word-break: break-all;
    overflow-wrap: break-word;
  }

  /* Navigation adjustments for French */
  html[lang="fr"] .navbar-button {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

.text-gradiant {
  background-image: linear-gradient(to right, #926BB9, #5A79FB,#2FBCF7);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

body {
  font-family: "Open Sans", serif;
  font-style: normal;
}

/* Button with text that might overflow */
.btn-with-text {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.btn-with-text span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  text-wrap: balance;
}

/* Fix for z-index issues with dropdowns */
.dropdown-container {
  position: relative;
  z-index: 1000;
}

.dropdown-menu {
  position: absolute;
  z-index: 1000;
}

.placeholder\:text-white::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.placeholder\:text-white::placeholder{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.last\:mb-0:last-child{
  margin-bottom: 0px;
}

.last\:border-b-0:last-child{
  border-bottom-width: 0px;
}

.checked\:bg-transparent:checked{
  background-color: transparent;
}

.checked\:text-white:checked{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:-translate-y-1:hover{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover{
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-\[\#DB1FEB\]:hover{
  --tw-border-opacity: 1;
  border-color: rgb(219 31 235 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.hover\:bg-\[\#3A3A3A\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(58 58 58 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#DB1FEB\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(219 31 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-800:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-80:hover{
  --tw-bg-opacity: 0.8;
}

.hover\:from-\[\#7721F3\]:hover{
  --tw-gradient-from: #7721F3 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(119 33 243 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-blue-700:hover{
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-green-700:hover{
  --tw-gradient-from: #15803d var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(21 128 61 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-pink-600:hover{
  --tw-gradient-from: #db2777 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 39 119 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-\[\#DB1FEB\]:hover{
  --tw-gradient-to: #DB1FEB var(--tw-gradient-to-position);
}

.hover\:to-blue-700:hover{
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}

.hover\:to-purple-700:hover{
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}

.hover\:to-teal-700:hover{
  --tw-gradient-to: #0f766e var(--tw-gradient-to-position);
}

.hover\:font-bold:hover{
  font-weight: 700;
}

.hover\:text-\[\#24CBFF\]:hover{
  --tw-text-opacity: 1;
  color: rgb(36 203 255 / var(--tw-text-opacity, 1));
}

.hover\:text-\[\#DB1FEB\]:hover{
  --tw-text-opacity: 1;
  color: rgb(219 31 235 / var(--tw-text-opacity, 1));
}

.hover\:text-black:hover{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-300:hover{
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.hover\:text-red-300:hover{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.hover\:text-red-800:hover{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover{
  text-decoration-line: underline;
}

.hover\:opacity-100:hover{
  opacity: 1;
}

.hover\:opacity-80:hover{
  opacity: 0.8;
}

.hover\:opacity-90:hover{
  opacity: 0.9;
}

.hover\:shadow-\[0_0_20px_rgba\(219\2c 31\2c 235\2c 0\.2\)\]:hover{
  --tw-shadow: 0 0 20px rgba(219,31,235,0.2);
  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_0_20px_rgba\(255\2c 255\2c 255\2c 0\.4\)\]:hover{
  --tw-shadow: 0 0 20px rgba(255,255,255,0.4);
  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-purple-500\/25:hover{
  --tw-shadow-color: rgb(168 85 247 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-purple-900\/20:hover{
  --tw-shadow-color: rgb(88 28 135 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:brightness-110:hover{
  --tw-brightness: brightness(1.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.hover\:drop-shadow-\[0_0_8px_rgba\(255\2c 255\2c 255\2c 0\.4\)\]:hover{
  --tw-drop-shadow: drop-shadow(0 0 8px rgba(255,255,255,0.4));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.hover\:transition:hover{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.hover\:duration-500:hover{
  transition-duration: 500ms;
}

.focus\:border-blue-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.focus\:border-purple-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:outline:focus{
  outline-style: solid;
}

.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-\[\#DB1FEB\]:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(219 31 235 / var(--tw-ring-opacity, 1));
}

.focus\:ring-blue-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}

.disabled\:bg-gray-400:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.disabled\:opacity-50:disabled{
  opacity: 0.5;
}

.group:hover .group-hover\:translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:opacity-70{
  opacity: 0.7;
}

@media (min-width: 640px){

  .sm\:ml-0{
    margin-left: 0px;
  }

  .sm\:ml-2{
    margin-left: 0.5rem;
  }

  .sm\:ml-\[11px\]{
    margin-left: 11px;
  }

  .sm\:inline{
    display: inline;
  }

  .sm\:hidden{
    display: none;
  }

  .sm\:h-14{
    height: 3.5rem;
  }

  .sm\:h-5{
    height: 1.25rem;
  }

  .sm\:h-\[18px\]{
    height: 18px;
  }

  .sm\:h-\[22px\]{
    height: 22px;
  }

  .sm\:w-1\/2{
    width: 50%;
  }

  .sm\:w-5{
    width: 1.25rem;
  }

  .sm\:w-\[18px\]{
    width: 18px;
  }

  .sm\:w-\[22px\]{
    width: 22px;
  }

  .sm\:max-w-full{
    max-width: 100%;
  }

  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:items-center{
    align-items: center;
  }

  .sm\:gap-4{
    gap: 1rem;
  }

  .sm\:gap-5{
    gap: 1.25rem;
  }

  .sm\:gap-6{
    gap: 1.5rem;
  }

  .sm\:gap-\[42px\]{
    gap: 42px;
  }

  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:px-2{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .sm\:px-3{
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .sm\:px-4{
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-\[30px\]{
    padding-left: 30px;
    padding-right: 30px;
  }

  .sm\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-\[18px\]{
    font-size: 18px;
  }

  .sm\:text-\[20px\]{
    font-size: 20px;
  }

  .sm\:text-\[22px\]{
    font-size: 22px;
  }

  .sm\:text-\[24px\]{
    font-size: 24px;
  }

  .sm\:text-\[26px\]{
    font-size: 26px;
  }

  .sm\:text-\[30px\]{
    font-size: 30px;
  }

  .sm\:text-\[40px\]{
    font-size: 40px;
  }

  .sm\:text-\[50px\]{
    font-size: 50px;
  }

  .sm\:text-\[60px\]{
    font-size: 60px;
  }

  .sm\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 768px){

  .md\:left-\[30px\]{
    left: 30px;
  }

  .md\:right-\[30px\]{
    right: 30px;
  }

  .md\:top-\[45px\]{
    top: 45px;
  }

  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .md\:mx-10{
    margin-left: 2.5rem;
    margin-right: 2.5rem;
  }

  .md\:-mt-\[110px\]{
    margin-top: -110px;
  }

  .md\:mb-0{
    margin-bottom: 0px;
  }

  .md\:ml-6{
    margin-left: 1.5rem;
  }

  .md\:ml-\[11px\]{
    margin-left: 11px;
  }

  .md\:mt-0{
    margin-top: 0px;
  }

  .md\:block{
    display: block;
  }

  .md\:flex{
    display: flex;
  }

  .md\:hidden{
    display: none;
  }

  .md\:h-12{
    height: 3rem;
  }

  .md\:h-16{
    height: 4rem;
  }

  .md\:h-24{
    height: 6rem;
  }

  .md\:h-4{
    height: 1rem;
  }

  .md\:h-\[22px\]{
    height: 22px;
  }

  .md\:h-\[400px\]{
    height: 400px;
  }

  .md\:h-\[50px\]{
    height: 50px;
  }

  .md\:h-\[75vh\]{
    height: 75vh;
  }

  .md\:min-h-\[540px\]{
    min-height: 540px;
  }

  .md\:min-h-full{
    min-height: 100%;
  }

  .md\:w-24{
    width: 6rem;
  }

  .md\:w-4{
    width: 1rem;
  }

  .md\:w-\[150px\]{
    width: 150px;
  }

  .md\:w-\[199px\]{
    width: 199px;
  }

  .md\:w-\[22px\]{
    width: 22px;
  }

  .md\:min-w-0{
    min-width: 0px;
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:items-start{
    align-items: flex-start;
  }

  .md\:items-center{
    align-items: center;
  }

  .md\:justify-start{
    justify-content: flex-start;
  }

  .md\:justify-between{
    justify-content: space-between;
  }

  .md\:gap-5{
    gap: 1.25rem;
  }

  .md\:gap-6{
    gap: 1.5rem;
  }

  .md\:gap-8{
    gap: 2rem;
  }

  .md\:gap-\[30px\]{
    gap: 30px;
  }

  .md\:p-16{
    padding: 4rem;
  }

  .md\:p-8{
    padding: 2rem;
  }

  .md\:px-10{
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:px-\[30px\]{
    padding-left: 30px;
    padding-right: 30px;
  }

  .md\:px-\[33px\]{
    padding-left: 33px;
    padding-right: 33px;
  }

  .md\:py-0{
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-2\.5{
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .md\:py-24{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .md\:py-4{
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .md\:py-\[27px\]{
    padding-top: 27px;
    padding-bottom: 27px;
  }

  .md\:py-\[31px\]{
    padding-top: 31px;
    padding-bottom: 31px;
  }

  .md\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-\[24px\]{
    font-size: 24px;
  }

  .md\:text-\[26px\]{
    font-size: 26px;
  }

  .md\:text-\[28px\]{
    font-size: 28px;
  }

  .md\:text-\[30px\]{
    font-size: 30px;
  }

  .md\:text-\[35px\]{
    font-size: 35px;
  }

  .md\:text-\[50px\]{
    font-size: 50px;
  }

  .md\:text-\[60px\]{
    font-size: 60px;
  }

  .md\:text-\[70px\]{
    font-size: 70px;
  }

  .md\:text-\[80px\]{
    font-size: 80px;
  }

  .md\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px){

  .lg\:col-span-1{
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .lg\:ml-3{
    margin-left: 0.75rem;
  }

  .lg\:mt-0{
    margin-top: 0px;
  }

  .lg\:flex{
    display: flex;
  }

  .lg\:hidden{
    display: none;
  }

  .lg\:h-\[22px\]{
    height: 22px;
  }

  .lg\:h-\[60px\]{
    height: 60px;
  }

  .lg\:h-\[85vh\]{
    height: 85vh;
  }

  .lg\:w-1\/2{
    width: 50%;
  }

  .lg\:w-\[180px\]{
    width: 180px;
  }

  .lg\:w-\[22px\]{
    width: 22px;
  }

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5{
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:flex-col{
    flex-direction: column;
  }

  .lg\:justify-end{
    justify-content: flex-end;
  }

  .lg\:gap-24{
    gap: 6rem;
  }

  .lg\:gap-6{
    gap: 1.5rem;
  }

  .lg\:gap-8{
    gap: 2rem;
  }

  .lg\:px-10{
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-12{
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .lg\:px-16{
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-24{
    padding-left: 6rem;
    padding-right: 6rem;
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-3{
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .lg\:py-4{
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .lg\:py-6{
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .lg\:pl-4{
    padding-left: 1rem;
  }

  .lg\:pr-4{
    padding-right: 1rem;
  }

  .lg\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-7xl{
    font-size: 4.5rem;
    line-height: 1;
  }

  .lg\:text-\[100px\]{
    font-size: 100px;
  }

  .lg\:text-\[28px\]{
    font-size: 28px;
  }

  .lg\:text-\[34px\]{
    font-size: 34px;
  }

  .lg\:text-\[80px\]{
    font-size: 80px;
  }

  .lg\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px){

  .xl\:absolute{
    position: absolute;
  }

  .xl\:col-span-1{
    grid-column: span 1 / span 1;
  }

  .xl\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .xl\:block{
    display: block;
  }

  .xl\:w-\[30\%\]{
    width: 30%;
  }

  .xl\:w-\[40\%\]{
    width: 40%;
  }

  .xl\:-translate-x-1\/3{
    --tw-translate-x: -33.333333%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .xl\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:grid-cols-5{
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .xl\:flex-row{
    flex-direction: row;
  }

  .xl\:gap-12{
    gap: 3rem;
  }

  .xl\:gap-4{
    gap: 1rem;
  }

  .xl\:gap-6{
    gap: 1.5rem;
  }

  .xl\:gap-8{
    gap: 2rem;
  }

  .xl\:space-x-8 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .xl\:px-16{
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .xl\:px-20{
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .xl\:py-0{
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .xl\:pl-6{
    padding-left: 1.5rem;
  }

  .xl\:text-\[32px\]{
    font-size: 32px;
  }

  .xl\:text-\[38px\]{
    font-size: 38px;
  }

  .xl\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .xl\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1536px){

  .\32xl\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .\32xl\:px-20{
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .\32xl\:px-48{
    padding-left: 12rem;
    padding-right: 12rem;
  }

  .\32xl\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .\32xl\:text-\[42px\]{
    font-size: 42px;
  }
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.js","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

