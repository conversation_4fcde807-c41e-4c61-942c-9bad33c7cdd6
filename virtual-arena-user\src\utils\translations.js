// Translations for the website
// English is the default language and serves as the key reference
// French translations are provided for all keys

const translations = {
  // Navigation
  nav: {
    home: {
      en: "Home",
      fr: "Accueil"
    },
    aboutUs: {
      en: "About Us",
      fr: "À Propos"
    },
    pricing: {
      en: "Pricing",
      fr: "Tarifs"
    },
    experience: {
      en: "Experience",
      fr: "Expérience"
    },
    more: {
      en: "More",
      fr: "Plus"
    },
    login: {
      en: "Login",
      fr: "Connexion"
    },
    signup: {
      en: "Signup",
      fr: "S'inscrire"
    },
    bookNow: {
      en: "Book Now",
      fr: "Réserver"
    },
    logout: {
      en: "Logout",
      fr: "Déconnexion"
    },
    wishlist: {
      en: "Wishlist",
      fr: "Favoris"
    },
    bookings: {
      en: "Bookings",
      fr: "Réservations"
    },
    orders: {
      en: "Orders",
      fr: "Commandes"
    },
    tournaments: {
      en: "Tournaments",
      fr: "Tournois"
    }
  },
  
  // More dropdown items
  moreItems: {
    deals: {
      en: "Deals & Membership",
      fr: "Offres & Abonnements"
    },
    contact: {
      en: "Contact Us",
      fr: "Contactez-nous"
    },
    merchandise: {
      en: "Shop",
      fr: "Boutique"
    }
  },
  
  // Experiences
  experiences: {
    ufoSpaceship: {
      en: "UFO Spaceship",
      fr: "Vaisseau OVNI"
    },
    vr360: {
      en: "VR 360",
      fr: "RV 360"
    },
    vrBattle: {
      en: "VR Battle",
      fr: "Bataille RV"
    },
    vrWarrior: {
      en: "VR WARRIOR (Kids)",
      fr: "GUERRIER RV (Enfants)"
    },
    vrCat: {
      en: "VR CAT (Kids)",
      fr: "CHAT RV (Enfants)"
    },
    freeRoaming: {
      en: "Free-roaming Arena",
      fr: "Arène de libre circulation"
    },
    photoBooth: {
      en: "Photo Booth",
      fr: "Cabine Photo"
    }
  },
  
  // Common UI elements
  ui: {
    language: {
      en: "Language",
      fr: "Langue"
    },
    english: {
      en: "English",
      fr: "Anglais"
    },
    french: {
      en: "French",
      fr: "Français"
    }
  },
  
  // SEO and meta
  meta: {
    title: {
      en: "Virtual Arena",
      fr: "Arène Virtuelle"
    },
    description: {
      en: "Experience virtual reality like never before at Virtual Arena",
      fr: "Découvrez la réalité virtuelle comme jamais auparavant à l'Arène Virtuelle"
    }
  }
};

export default translations; 