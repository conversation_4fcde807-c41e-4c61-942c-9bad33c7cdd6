# Example environment variables for Virtual Arena frontend
# Copy this file to .env.local (for development) or .env.production (on server)
# and adjust the values accordingly.

# Base URL of your production API (backend) – MUST be HTTPS in production
NEXT_PUBLIC_API_URL=https://vrtualarena.ca/api/v1

# Base URL where uploaded media is served (same host as production frontend)
NEXT_PUBLIC_MEDIA_BASE_URL=https://vrtualarena.ca
